/************************* 串口测试程序 *************************/
#include "ti_msp_dl_config.h"
#include "stdio.h"
#include "string.h"
#include "USART.h"

/************************ 全局变量 ************************/
volatile unsigned char uart_data = 0;
unsigned char test_buff[256] = {0};

/************************ 函数声明 ************************/
void simple_delay(uint32_t count);

int main(void)
{
    // 系统初始化 - 只调用一次
    SYSCFG_DL_init();

    // 清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    // 使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    // 等待系统稳定
    simple_delay(1000000);

    // 测试1: 发送单个字符
    uart0_send_char('S');
    simple_delay(100000);

    uart0_send_char('T');
    simple_delay(100000);

    uart0_send_char('A');
    simple_delay(100000);

    uart0_send_char('R');
    simple_delay(100000);

    uart0_send_char('T');
    simple_delay(100000);

    uart0_send_char('\r');
    uart0_send_char('\n');
    simple_delay(100000);

    // 测试2: 发送字符串
    sprintf((char *)test_buff, "UART Test Start!\r\n");
    uart0_send_string((char *)test_buff);
    memset(test_buff, 0, 256);
    simple_delay(500000);

    // 测试3: 使用printf
    printf("Printf Test: Hello World!\r\n");
    simple_delay(500000);

    // 测试4: 循环发送计数
    uint32_t counter = 0;

    while (1)
    {
        // 每秒发送一次计数
        sprintf((char *)test_buff, "Counter: %lu\r\n", counter);
        uart0_send_string((char *)test_buff);
        memset(test_buff, 0, 256);

        counter++;

        // 简单延时约1秒
        simple_delay(8000000);

        // 每10次计数发送一次printf测试
        if (counter % 10 == 0)
        {
            printf("Printf Counter: %lu\r\n", counter);
        }
    }
}

// 简单的延时函数
void simple_delay(uint32_t count)
{
    volatile uint32_t i;
    for (i = 0; i < count; i++)
    {
        __NOP();
    }
}

// 串口中断处理函数
void UART_0_INST_IRQHandler(void)
{
    // 检查是什么串口中断
    switch (DL_UART_getPendingInterrupt(UART_0_INST))
    {
    case DL_UART_IIDX_RX: // 如果是接收中断
        // 从发送寄存器中读数据保存在变量中
        uart_data = DL_UART_Main_receiveData(UART_0_INST);
        // 将接收到的数据立即发送出去（回显）
        uart0_send_char(uart_data);
        break;

    default: // 其他的串口中断
        break;
    }
}
