/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.1+4034"}
 */

/**
 * Import the modules used in this configuration.
 */
const Board   = scripting.addModule("/ti/driverlib/Board");
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SPI     = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1    = SPI.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const TIMER2  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.enable       = true;
pinFunction4.inputFreq    = 40;
pinFunction4.HFXTStartup  = 10;
pinFunction4.HFCLKMonitor = true;

Board.peripheral.swclkPin.$assign = "PA20";
Board.peripheral.swdioPin.$assign = "PA19";

GPIO1.port                               = "PORTB";
GPIO1.$name                              = "LED1";
GPIO1.associatedPins[0].$name            = "PIN_22";
GPIO1.associatedPins[0].assignedPin      = "22";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";

GPIO2.$name                              = "KEY1";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_21";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPin      = "21";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";

GPIO3.$name                          = "IIC_Software";
GPIO3.port                           = "PORTA";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name        = "SCL";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].assignedPort = "PORTA";
GPIO3.associatedPins[0].assignedPin  = "1";
GPIO3.associatedPins[0].ioStructure  = "OD";
GPIO3.associatedPins[0].pin.$assign  = "PA1";
GPIO3.associatedPins[1].$name        = "SDA";
GPIO3.associatedPins[1].initialValue = "SET";
GPIO3.associatedPins[1].assignedPort = "PORTA";
GPIO3.associatedPins[1].assignedPin  = "0";
GPIO3.associatedPins[1].ioStructure  = "OD";
GPIO3.associatedPins[1].pin.$assign  = "PA0";

GPIO4.$name                          = "GPIO_LCD";
GPIO4.associatedPins.create(4);
GPIO4.associatedPins[0].$name        = "PIN_RES";
GPIO4.associatedPins[0].assignedPort = "PORTB";
GPIO4.associatedPins[0].assignedPin  = "10";
GPIO4.associatedPins[1].$name        = "PIN_DC";
GPIO4.associatedPins[1].assignedPort = "PORTB";
GPIO4.associatedPins[1].assignedPin  = "11";
GPIO4.associatedPins[2].$name        = "PIN_CS";
GPIO4.associatedPins[2].assignedPort = "PORTB";
GPIO4.associatedPins[2].assignedPin  = "14";
GPIO4.associatedPins[3].$name        = "PIN_BLK";
GPIO4.associatedPins[3].assignedPort = "PORTB";
GPIO4.associatedPins[3].assignedPin  = "26";
GPIO4.associatedPins[3].pin.$assign  = "PB26";

GPIO5.$name                              = "GPIO_KEY";
GPIO5.associatedPins.create(4);
GPIO5.associatedPins[0].$name            = "PIN_UP";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].assignedPort     = "PORTA";
GPIO5.associatedPins[0].assignedPin      = "9";
GPIO5.associatedPins[1].$name            = "PIN_LEFT";
GPIO5.associatedPins[1].direction        = "INPUT";
GPIO5.associatedPins[1].internalResistor = "PULL_UP";
GPIO5.associatedPins[1].assignedPort     = "PORTA";
GPIO5.associatedPins[1].assignedPin      = "8";
GPIO5.associatedPins[2].$name            = "PIN_RIGHT";
GPIO5.associatedPins[2].direction        = "INPUT";
GPIO5.associatedPins[2].internalResistor = "PULL_UP";
GPIO5.associatedPins[2].assignedPort     = "PORTA";
GPIO5.associatedPins[2].assignedPin      = "31";
GPIO5.associatedPins[3].direction        = "INPUT";
GPIO5.associatedPins[3].internalResistor = "PULL_UP";
GPIO5.associatedPins[3].assignedPort     = "PORTA";
GPIO5.associatedPins[3].assignedPin      = "28";
GPIO5.associatedPins[3].$name            = "PIN_DOWN";

GPIO6.$name                          = "TB6612";
GPIO6.associatedPins.create(4);
GPIO6.associatedPins[0].$name        = "AIN1";
GPIO6.associatedPins[0].initialValue = "SET";
GPIO6.associatedPins[0].assignedPort = "PORTA";
GPIO6.associatedPins[0].assignedPin  = "14";
GPIO6.associatedPins[0].pin.$assign  = "PA14";
GPIO6.associatedPins[1].initialValue = "SET";
GPIO6.associatedPins[1].assignedPort = "PORTA";
GPIO6.associatedPins[1].$name        = "AIN2";
GPIO6.associatedPins[1].pin.$assign  = "PA15";
GPIO6.associatedPins[2].initialValue = "SET";
GPIO6.associatedPins[2].assignedPort = "PORTA";
GPIO6.associatedPins[2].$name        = "BIN1";
GPIO6.associatedPins[3].initialValue = "SET";
GPIO6.associatedPins[3].assignedPort = "PORTA";
GPIO6.associatedPins[3].$name        = "BIN2";

GPIO7.$name                               = "GPIO_ENCODER";
GPIO7.associatedPins.create(4);
GPIO7.associatedPins[0].direction         = "INPUT";
GPIO7.associatedPins[0].interruptEn       = true;
GPIO7.associatedPins[0].interruptPriority = "0";
GPIO7.associatedPins[0].polarity          = "RISE";
GPIO7.associatedPins[0].assignedPort      = "PORTB";
GPIO7.associatedPins[0].$name             = "PIN_A_L";
GPIO7.associatedPins[0].pin.$assign       = "PB6";
GPIO7.associatedPins[1].direction         = "INPUT";
GPIO7.associatedPins[1].assignedPort      = "PORTB";
GPIO7.associatedPins[1].interruptEn       = true;
GPIO7.associatedPins[1].interruptPriority = "0";
GPIO7.associatedPins[1].polarity          = "RISE";
GPIO7.associatedPins[1].$name             = "PIN_B_L";
GPIO7.associatedPins[1].pin.$assign       = "PB7";
GPIO7.associatedPins[2].$name             = "PIN_A_R";
GPIO7.associatedPins[2].direction         = "INPUT";
GPIO7.associatedPins[2].interruptEn       = true;
GPIO7.associatedPins[2].interruptPriority = "0";
GPIO7.associatedPins[2].polarity          = "RISE";
GPIO7.associatedPins[2].pin.$assign       = "PB15";
GPIO7.associatedPins[3].$name             = "PIN_B_R";
GPIO7.associatedPins[3].direction         = "INPUT";
GPIO7.associatedPins[3].interruptEn       = true;
GPIO7.associatedPins[3].interruptPriority = "0";
GPIO7.associatedPins[3].polarity          = "RISE";
GPIO7.associatedPins[3].pin.$assign       = "PB16";

I2C1.$name                     = "I2C_0";
I2C1.basicEnableController     = true;
I2C1.advControllerRXFIFOTRIG   = "BYTES_8";
I2C1.advControllerTXFIFOTRIG   = "BYTES_7";
I2C1.peripheral.$assign        = "I2C0";
I2C1.peripheral.sdaPin.$assign = "PA10";
I2C1.peripheral.sclPin.$assign = "PA11";
I2C1.sdaPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric7";
I2C1.sclPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric8";

PWM1.$name                              = "PWM_LED";
PWM1.clockDivider                       = 8;
PWM1.ccIndex                            = [1];
PWM1.timerStartTimer                    = true;
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.peripheral.$assign                 = "TIMG6";
PWM1.peripheral.ccp1Pin.$assign         = "PB27";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC0";

PWM2.timerStartTimer                    = true;
PWM2.$name                              = "PWM_0";
PWM2.clockDivider                       = 8;
PWM2.peripheral.$assign                 = "TIMG7";
PWM2.peripheral.ccp0Pin.$assign         = "PA26";
PWM2.peripheral.ccp1Pin.$assign         = "PA27";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.PWM_CHANNEL_0.invert               = true;
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.invert               = true;
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";

SPI1.$name                              = "SPI_LCD";
SPI1.targetBitRate                      = 32000000;
SPI1.frameFormat                        = "MOTO3";
SPI1.direction                          = "PICO";
SPI1.sclkPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.sclkPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
SPI1.mosiPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion  = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
SPI1.peripheral.$assign                 = "SPI1";
SPI1.peripheral.sclkPin.$assign         = "PB9";
SPI1.peripheral.mosiPin.$assign         = "PB8";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

SYSTICK.periodEnable  = true;
SYSTICK.period        = 80;
SYSTICK.systickEnable = true;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 100;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "1ms";
TIMER1.peripheral.$assign = "TIMG0";

TIMER2.$name             = "TIMER_TICK";
TIMER2.timerClkDiv       = 8;
TIMER2.timerClkPrescale  = 10;
TIMER2.timerMode         = "PERIODIC";
TIMER2.timerPeriod       = "20 ms";
TIMER2.interrupts        = ["ZERO"];
TIMER2.interruptPriority = "3";
TIMER2.timerStartTimer   = true;

UART1.$name                    = "UART_0";
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.rxPin.$assign = "PB1";
UART1.peripheral.txPin.$assign = "PB0";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB21";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PB11";
GPIO4.associatedPins[2].pin.$suggestSolution       = "PB14";
GPIO5.associatedPins[0].pin.$suggestSolution       = "PA9";
GPIO5.associatedPins[1].pin.$suggestSolution       = "PA8";
GPIO5.associatedPins[2].pin.$suggestSolution       = "PA31";
GPIO5.associatedPins[3].pin.$suggestSolution       = "PA28";
GPIO6.associatedPins[2].pin.$suggestSolution       = "PA13";
GPIO6.associatedPins[3].pin.$suggestSolution       = "PA12";
TIMER2.peripheral.$suggestSolution                 = "TIMA0";
UART1.peripheral.$suggestSolution                  = "UART0";
