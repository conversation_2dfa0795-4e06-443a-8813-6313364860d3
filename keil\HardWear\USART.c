#include "USART.h"
#include "stdio.h"
// ���ڷ��͵����ַ�
void uart0_send_char(char ch)
{
    // ������0æ��ʱ��ȴ�����æ��ʱ���ٷ��ʹ��������ַ�
    while (DL_UART_Main_isBusy(UART_0_INST) == true)
        ;
    // ���͵����ַ�
    DL_UART_Main_transmitData(UART_0_INST, ch);
}
// ���ڷ����ַ���
void uart0_send_string(char *str)
{
    // ��ǰ�ַ�����ַ���ڽ�β ���� �ַ����׵�ַ��Ϊ��
    while (*str != 0 && str != 0)
    {
        // �����ַ����׵�ַ�е��ַ��������ڷ������֮���׵�ַ����
        uart0_send_char(*str++);
    }
}

int fputc(int ch, FILE *stream)
{
    while (DL_UART_Main_isBusy(UART_0_INST) == true)
        ;

    DL_UART_Main_transmitData(UART_0_INST, ch);

    return ch;
}