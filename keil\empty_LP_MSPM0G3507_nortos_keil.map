Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.ARM.exidx.text._sys_exit) refers to empty.o(.text._sys_exit) for [Anonymous Symbol]
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_timer.o(.text.timer_init) for timer_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to bsp_motor_hallencoder.o(.text.encoder_init) for encoder_init
    empty.o(.text.main) refers to hw_lcd.o(.text.lcd_init) for lcd_init
    empty.o(.text.main) refers to iic.o(.text.jy61pInit) for jy61pInit
    empty.o(.text.main) refers to sprintf.o(.text) for sprintf
    empty.o(.text.main) refers to usart.o(.text.uart0_send_string) for uart0_send_string
    empty.o(.text.main) refers to rt_memclr.o(.text) for __aeabi_memclr
    empty.o(.text.main) refers to printf.o(.text) for printf
    empty.o(.text.main) refers to iic.o(.text.get_angle) for get_angle
    empty.o(.text.main) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.main) refers to time.o(.text.delay_ms) for delay_ms
    empty.o(.text.main) refers to empty.o(.bss.rx_buff) for rx_buff
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to sensor.o(.text.UpdatePosition) for UpdatePosition
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for get_encoder_count_L
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for get_encoder_count_R
    empty.o(.text.PID_operation) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.PID_operation) refers to filter.o(.text.Kalman_Update) for Kalman_Update
    empty.o(.text.PID_operation) refers to pid.o(.text.PID_Calculate) for PID_Calculate
    empty.o(.text.PID_operation) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    empty.o(.text.PID_operation) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    empty.o(.text.PID_operation) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.PID_operation) refers to bsp_tb6612.o(.text.AB_Control) for AB_Control
    empty.o(.text.PID_operation) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.PID_operation) refers to printf.o(.text) for printf
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_kalman) for left_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_kalman) for right_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_kalman) for posion_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_pid) for posion_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_speed_pid) for left_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_speed_pid) for right_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_left_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_right_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_L) for Num_L
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_R) for Num_R
    empty.o(.text.PID_operation) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.PID_operation) refers to empty.o(.text.PID_operation) for [Anonymous Symbol]
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    empty.o(.text.UART0_IRQHandler) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.bss.uart_data) for uart_data
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers to empty.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to empty.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_UART_receiveData) refers to empty.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.data.TIMG0_IRQHandler.timer_count) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.led_flash_time) for led_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.bmq_flash_time) for bmq_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.PID_flash_time) for PID_flash_time
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to empty.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to hw_timer.o(.text.TIMA0_IRQHandler) for TIMA0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for SYSCFG_DL_PWM_LED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for SYSCFG_DL_TIMER_TICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for SYSCFG_DL_SPI_LCD_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for DL_I2C_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for DL_SPI_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for DL_I2C_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for DL_SPI_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for DL_GPIO_initPeripheralInputFunctionFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for DL_GPIO_enableHiZ
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for DL_GPIO_initDigitalOutputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for DL_SPI_setBitRateSerialClockDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for DL_SPI_setFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for DL_SPI_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.text.LED_flash) refers to time.o(.text.delay_ms) for delay_ms
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_flash) refers to led.o(.text.LED_flash) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    key.o(.ARM.exidx.text.KEY_control_LED) refers to key.o(.text.KEY_control_LED) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_setPins) refers to key.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to key.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    key.o(.text.Get_KEY) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.Get_KEY) refers to time.o(.text.delay_ms) for delay_ms
    key.o(.ARM.exidx.text.Get_KEY) refers to key.o(.text.Get_KEY) for [Anonymous Symbol]
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_isBusy) refers to usart.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_transmitData) refers to usart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    usart.o(.text.uart0_send_string) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    pwm.o(.text.PWM_LED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.text.PWM_LED) refers to time.o(.text.delay_ms) for delay_ms
    pwm.o(.ARM.exidx.text.PWM_LED) refers to pwm.o(.text.PWM_LED) for [Anonymous Symbol]
    iic.o(.text.jy61pInit) refers to iic.o(.text.writeDataJy61p) for writeDataJy61p
    iic.o(.text.jy61pInit) refers to time.o(.text.delay_ms) for delay_ms
    iic.o(.text.jy61pInit) refers to iic.o(.bss.Gyro_Structure) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.jy61pInit) refers to iic.o(.text.jy61pInit) for [Anonymous Symbol]
    iic.o(.text.writeDataJy61p) refers to iic.o(.text.IIC_Start) for IIC_Start
    iic.o(.text.writeDataJy61p) refers to iic.o(.text.Send_Byte) for Send_Byte
    iic.o(.text.writeDataJy61p) refers to iic.o(.text.I2C_WaitAck) for I2C_WaitAck
    iic.o(.text.writeDataJy61p) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.ARM.exidx.text.writeDataJy61p) refers to iic.o(.text.writeDataJy61p) for [Anonymous Symbol]
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Start) refers to time.o(.text.delay_us) for delay_us
    iic.o(.ARM.exidx.text.IIC_Start) refers to iic.o(.text.IIC_Start) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to iic.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to iic.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_setPins) refers to iic.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Stop) refers to time.o(.text.delay_us) for delay_us
    iic.o(.ARM.exidx.text.IIC_Stop) refers to iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Send_Ack) refers to time.o(.text.delay_us) for delay_us
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.ARM.exidx.text.IIC_Send_Ack) refers to iic.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.text.I2C_WaitAck) refers to time.o(.text.delay_us) for delay_us
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.I2C_WaitAck) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.ARM.exidx.text.I2C_WaitAck) refers to iic.o(.text.I2C_WaitAck) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to iic.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_readPins) refers to iic.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    iic.o(.text.Send_Byte) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.Send_Byte) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.Send_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.Send_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.Send_Byte) refers to time.o(.text.delay_us) for delay_us
    iic.o(.ARM.exidx.text.Send_Byte) refers to iic.o(.text.Send_Byte) for [Anonymous Symbol]
    iic.o(.text.Read_Byte) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.Read_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.Read_Byte) refers to time.o(.text.delay_us) for delay_us
    iic.o(.text.Read_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.Read_Byte) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.ARM.exidx.text.Read_Byte) refers to iic.o(.text.Read_Byte) for [Anonymous Symbol]
    iic.o(.text.readDataJy61p) refers to iic.o(.text.IIC_Start) for IIC_Start
    iic.o(.text.readDataJy61p) refers to iic.o(.text.Send_Byte) for Send_Byte
    iic.o(.text.readDataJy61p) refers to iic.o(.text.I2C_WaitAck) for I2C_WaitAck
    iic.o(.text.readDataJy61p) refers to time.o(.text.delay_us) for delay_us
    iic.o(.text.readDataJy61p) refers to iic.o(.text.Read_Byte) for Read_Byte
    iic.o(.text.readDataJy61p) refers to iic.o(.text.IIC_Send_Ack) for IIC_Send_Ack
    iic.o(.text.readDataJy61p) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.ARM.exidx.text.readDataJy61p) refers to iic.o(.text.readDataJy61p) for [Anonymous Symbol]
    iic.o(.text.get_angle) refers to iic.o(.text.readDataJy61p) for readDataJy61p
    iic.o(.text.get_angle) refers to printf.o(.text) for printf
    iic.o(.text.get_angle) refers to dflti.o(.text) for __aeabi_i2d
    iic.o(.text.get_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    iic.o(.text.get_angle) refers to dmul.o(.text) for __aeabi_dmul
    iic.o(.text.get_angle) refers to d2f.o(.text) for __aeabi_d2f
    iic.o(.text.get_angle) refers to f2d.o(.text) for __aeabi_f2d
    iic.o(.text.get_angle) refers to dcmp.o(i._dleq) for __aeabi_dcmple
    iic.o(.text.get_angle) refers to daddsub.o(.text) for __aeabi_dadd
    iic.o(.text.get_angle) refers to dcmp.o(i._dgeq) for __aeabi_dcmpge
    iic.o(.text.get_angle) refers to iic.o(.rodata.str1.1) for [Anonymous Symbol]
    iic.o(.text.get_angle) refers to iic.o(.bss.Gyro_Structure) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.get_angle) refers to iic.o(.text.get_angle) for [Anonymous Symbol]
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_isBusy) for DL_SPI_isBusy
    hw_lcd.o(.ARM.exidx.text.spi_write_bus) refers to hw_lcd.o(.text.spi_write_bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy) refers to hw_lcd.o(.text.DL_SPI_isBusy) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.spi_write_bus) for spi_write_bus
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_lcd.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.lcd_init) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.lcd_init) refers to hw_lcd.o(.text.lcd_init) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Fill) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_DrawVerrticalLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    hw_lcd.o(.text.Draw_Circle) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Draw_Circle) refers to hw_lcd.o(.text.Draw_Circle) for [Anonymous Symbol]
    hw_lcd.o(.text.Drawarc) refers to dflti.o(.text) for __aeabi_ui2d
    hw_lcd.o(.text.Drawarc) refers to sqrt.o(i.sqrt) for sqrt
    hw_lcd.o(.text.Drawarc) refers to dfixi.o(.text) for __aeabi_d2iz
    hw_lcd.o(.text.Drawarc) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Drawarc) refers to hw_lcd.o(.text.Drawarc) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.Drawarc) for Drawarc
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    hw_lcd.o(.ARM.exidx.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_ArcRect) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for LCD_ShowChinese12x12
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.rodata.tfont12) for tfont12
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.rodata.tfont16) for tfont16
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.rodata.tfont24) for tfont24
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.rodata.tfont32) for tfont32
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChar) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_3216) for ascii_3216
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_2412) for ascii_2412
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1608) for ascii_1608
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1206) for ascii_1206
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowString) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.mypow) refers to hw_lcd.o(.text.mypow) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowIntNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowIntNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to ffixui.o(.text) for __aeabi_f2uiz
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_x_center) refers to oled.o(.text.disp_x_center) for [Anonymous Symbol]
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_string_rect) refers to oled.o(.text.disp_string_rect) for [Anonymous Symbol]
    oled.o(.text.disp_select_box) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    oled.o(.ARM.exidx.text.disp_select_box) refers to oled.o(.text.disp_select_box) for [Anonymous Symbol]
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.text.ui_home_page) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_x_center) for disp_x_center
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_string_rect) for disp_string_rect
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_select_box) for disp_select_box
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.text.ui_home_page) refers to oled.o(.rodata.str1.1) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.ui_home_page) refers to oled.o(.text.ui_home_page) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_key.o(.text.key_scan) refers to hw_key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_key.o(.ARM.exidx.text.key_scan) refers to hw_key.o(.text.key_scan) for [Anonymous Symbol]
    hw_key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.text.PID_Calculate) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.PID_Calculate) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid.o(.text.PID_Calculate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.PID_Calculate) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.PID_Calculate) refers to fdiv.o(.text) for __aeabi_fdiv
    pid.o(.ARM.exidx.text.PID_Calculate) refers to pid.o(.text.PID_Calculate) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.encoder_init) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    bsp_tb6612.o(.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.TB6612_Motor_Stop) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_tb6612.o(.text.AB_Control) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    bsp_tb6612.o(.ARM.exidx.text.AB_Control) refers to bsp_tb6612.o(.text.AB_Control) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    hw_timer.o(.ARM.exidx.text.timer_init) refers to hw_timer.o(.text.timer_init) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    hw_timer.o(.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for encoder_update_L
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for encoder_update_R
    hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.TIMA0_IRQHandler) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.Kalman_Init) refers to filter.o(.text.Kalman_Init) for [Anonymous Symbol]
    filter.o(.text.Kalman_Update) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    filter.o(.text.Kalman_Update) refers to fdiv.o(.text) for __aeabi_fdiv
    filter.o(.text.Kalman_Update) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    filter.o(.text.Kalman_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    filter.o(.ARM.exidx.text.Kalman_Update) refers to filter.o(.text.Kalman_Update) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for hardware_IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for hardware_IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for hardware_IIC_WirteByte
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for hardware_IIC_WirteBytes
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to time.o(.text.delay_ms) for delay_ms
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.bss.IIC_write_buff) for IIC_write_buff
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_Get_Normalize) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    hw_i2c.o(.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to hw_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.text.delay_ms) refers to time.o(.text.delay_us) for delay_us
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    sensor.o(.text.CalculatePositionFromDigital) refers to fflti.o(.text) for __aeabi_i2f
    sensor.o(.text.CalculatePositionFromDigital) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    sensor.o(.text.CalculatePositionFromDigital) refers to fdiv.o(.text) for __aeabi_fdiv
    sensor.o(.text.CalculatePositionFromDigital) refers to sensor.o(.rodata.position_weights) for [Anonymous Symbol]
    sensor.o(.ARM.exidx.text.CalculatePositionFromDigital) refers to sensor.o(.text.CalculatePositionFromDigital) for [Anonymous Symbol]
    sensor.o(.text.UpdatePosition) refers to hardware_iic.o(.text.IIC_Get_Digtal) for IIC_Get_Digtal
    sensor.o(.text.UpdatePosition) refers to sensor.o(.text.CalculatePositionFromDigital) for CalculatePositionFromDigital
    sensor.o(.ARM.exidx.text.UpdatePosition) refers to sensor.o(.text.UpdatePosition) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    printf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    printf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    printf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    printf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    printf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    printf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    printf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    printf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    printf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    printf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    printf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    printf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    printf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    printf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    printf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    printf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    printf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    printf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    printf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    printf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    printf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    printf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    printf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    printf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    printf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    printf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    printf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    printf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    printf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    printf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    printf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    printf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    printf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    printf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    printf.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    sprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    sprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    sprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    sprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    sprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    sprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    sprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    sprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    sprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    sprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    sprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    sprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    sprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    sprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    sprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    sprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    sprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    sprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    sprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    sprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    sprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    sprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    sprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    sprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    sprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    sprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    sprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    sprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    sprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    sprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    sprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    sprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    sprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    sprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._deq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._deq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._dgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgeq) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgr) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dleq) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dls) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dneq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dgef.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgef.o(x$fpl$dgeqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dlef.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dlef.o(x$fpl$dleqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(.text) for _btod_d2e
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    bigflt0.o(.text) refers to btod.o(.text) for _btod_emul
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(.text) refers to btod.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    ieee_status.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    btod_accurate_common.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to empty.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.text.PID_operation), (340 bytes).
    Removing empty.o(.ARM.exidx.text.PID_operation), (8 bytes).
    Removing empty.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing empty.o(.bss.delay_times), (4 bytes).
    Removing empty.o(.bss.Anolog), (8 bytes).
    Removing empty.o(.bss.Normal), (8 bytes).
    Removing empty.o(.bss.PID_operation.total_left_speed), (4 bytes).
    Removing empty.o(.bss.PID_operation.total_right_speed), (4 bytes).
    Removing empty.o(.bss.PID_operation.total_speed), (4 bytes).
    Removing empty.o(.bss.left_kalman), (16 bytes).
    Removing empty.o(.bss.right_kalman), (16 bytes).
    Removing empty.o(.bss.posion_kalman), (16 bytes).
    Removing empty.o(.bss.posion_pid), (20 bytes).
    Removing empty.o(.bss.left_speed_pid), (20 bytes).
    Removing empty.o(.bss.right_speed_pid), (20 bytes).
    Removing empty.o(.bss.temporary_str), (40 bytes).
    Removing empty.o(.bss.llll), (4 bytes).
    Removing empty.o(.bss.rrrr), (4 bytes).
    Removing empty.o(.bss.Digtal), (1 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (128 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (136 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_flash), (52 bytes).
    Removing led.o(.ARM.exidx.text.LED_flash), (8 bytes).
    Removing led.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.KEY_control_LED), (48 bytes).
    Removing key.o(.ARM.exidx.text.KEY_control_LED), (8 bytes).
    Removing key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing key.o(.text.Get_KEY), (80 bytes).
    Removing key.o(.ARM.exidx.text.Get_KEY), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.PWM_LED), (120 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_LED), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.ARM.exidx.text.jy61pInit), (8 bytes).
    Removing iic.o(.ARM.exidx.text.writeDataJy61p), (8 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing iic.o(.ARM.exidx.text.I2C_WaitAck), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing iic.o(.ARM.exidx.text.Send_Byte), (8 bytes).
    Removing iic.o(.ARM.exidx.text.Read_Byte), (8 bytes).
    Removing iic.o(.ARM.exidx.text.readDataJy61p), (8 bytes).
    Removing iic.o(.ARM.exidx.text.get_angle), (8 bytes).
    Removing iic.o(.rodata..L__const.jy61pInit.unlock_reg1), (2 bytes).
    Removing iic.o(.rodata..L__const.jy61pInit.unlock_reg), (2 bytes).
    Removing hw_lcd.o(.text), (0 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.spi_write_bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_DATA), (28 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Address_Set), (62 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.lcd_init), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Fill), (92 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawPoint), (32 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawLine), (268 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawVerrticalLine), (66 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawRectangle), (82 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing hw_lcd.o(.text.Draw_Circle), (220 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing hw_lcd.o(.text.Drawarc), (338 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Drawarc), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ArcRect), (330 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ArcRect), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese), (296 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese12x12), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese16x16), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese24x24), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese32x32), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChar), (476 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowString), (98 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing hw_lcd.o(.text.mypow), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowIntNum), (244 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowFloatNum1), (252 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowPicture), (128 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing hw_lcd.o(.rodata.ascii_1206), (1140 bytes).
    Removing hw_lcd.o(.rodata.ascii_1608), (1520 bytes).
    Removing hw_lcd.o(.rodata.ascii_2412), (4560 bytes).
    Removing hw_lcd.o(.rodata.ascii_3216), (6080 bytes).
    Removing hw_lcd.o(.rodata.tfont12), (130 bytes).
    Removing hw_lcd.o(.rodata.tfont16), (374 bytes).
    Removing hw_lcd.o(.rodata.tfont24), (222 bytes).
    Removing hw_lcd.o(.rodata.tfont32), (130 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.disp_x_center), (100 bytes).
    Removing oled.o(.ARM.exidx.text.disp_x_center), (8 bytes).
    Removing oled.o(.text.disp_string_rect), (136 bytes).
    Removing oled.o(.ARM.exidx.text.disp_string_rect), (8 bytes).
    Removing oled.o(.text.disp_select_box), (234 bytes).
    Removing oled.o(.ARM.exidx.text.disp_select_box), (8 bytes).
    Removing oled.o(.text.ui_home_page), (216 bytes).
    Removing oled.o(.ARM.exidx.text.ui_home_page), (8 bytes).
    Removing oled.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing oled.o(.rodata.str1.1), (37 bytes).
    Removing hw_key.o(.text), (0 bytes).
    Removing hw_key.o(.text.key_scan), (140 bytes).
    Removing hw_key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing hw_key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing hw_key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.PID_Init), (44 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid.o(.text.PID_Calculate), (184 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Calculate), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text), (0 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_count_L), (32 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_count_R), (32 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_L), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_R), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing bsp_tb6612.o(.text), (0 bytes).
    Removing bsp_tb6612.o(.text.TB6612_Motor_Stop), (56 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop), (8 bytes).
    Removing bsp_tb6612.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bsp_tb6612.o(.text.AB_Control), (176 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.AB_Control), (8 bytes).
    Removing bsp_tb6612.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_timer.o(.text), (0 bytes).
    Removing hw_timer.o(.ARM.exidx.text.timer_init), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing hw_timer.o(.bss.Num_L), (4 bytes).
    Removing hw_timer.o(.bss.Num_R), (4 bytes).
    Removing filter.o(.text), (0 bytes).
    Removing filter.o(.text.Kalman_Init), (42 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Init), (8 bytes).
    Removing filter.o(.text.Kalman_Update), (108 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Update), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.text.IIC_ReadByte), (28 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (36 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.text.Ping), (50 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Digtal), (24 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (34 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Normalize), (84 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize), (8 bytes).
    Removing hardware_iic.o(.bss.IIC_write_buff), (10 bytes).
    Removing hw_i2c.o(.text), (0 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteByte), (120 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing hw_i2c.o(.text.DL_I2C_getControllerStatus), (20 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing hw_i2c.o(.text.DL_I2C_startControllerTransfer), (80 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteBytes), (196 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_ReadByte), (176 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing hw_i2c.o(.text.DL_I2C_receiveControllerData), (20 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_ReadBytes), (220 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing hw_i2c.o(.text.DL_Common_updateReg), (40 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing sensor.o(.text), (0 bytes).
    Removing sensor.o(.text.CalculatePositionFromDigital), (128 bytes).
    Removing sensor.o(.ARM.exidx.text.CalculatePositionFromDigital), (8 bytes).
    Removing sensor.o(.text.UpdatePosition), (28 bytes).
    Removing sensor.o(.ARM.exidx.text.UpdatePosition), (8 bytes).
    Removing sensor.o(.rodata.position_weights), (32 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

529 unused section(s) (total 30832 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.c                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv6m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/dsqrt.c                  0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/ieee_status.c            0x00000000   Number         0  ieee_status.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dgeqf6m.s                       0x00000000   Number         0  dgef.o ABSOLUTE
    ../fplib/dleqf6m.s                       0x00000000   Number         0  dlef.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Filter.c                                 0x00000000   Number         0  filter.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    PID.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    Sensor.c                                 0x00000000   Number         0  sensor.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    USART.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    bsp_motor_hallencoder.c                  0x00000000   Number         0  bsp_motor_hallencoder.o ABSOLUTE
    bsp_tb6612.c                             0x00000000   Number         0  bsp_tb6612.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    hw_i2c.c                                 0x00000000   Number         0  hw_i2c.o ABSOLUTE
    hw_key.c                                 0x00000000   Number         0  hw_key.o ABSOLUTE
    hw_lcd.c                                 0x00000000   Number         0  hw_lcd.o ABSOLUTE
    hw_timer.c                               0x00000000   Number         0  hw_timer.o ABSOLUTE
    iic.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x00000166   Section       10  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x00000170   Section       10  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0000017a   Section       10  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x00000184   Section       10  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x0000018e   Section       10  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x00000198   Section       10  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x000001a2   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x000001ac   Section       10  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x000001b6   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x000001c0   Section       10  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x000001ca   Section       10  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x000001d4   Section       10  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x000001de   Section       10  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x000001e8   Section       10  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x000001f2   Section       10  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x000001fc   Section       10  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x00000206   Section       10  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x00000210   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x0000021a   Section       10  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x00000224   Section       10  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0000022e   Section       10  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x00000238   Section       10  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x00000242   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x00000246   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x00000248   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0000024e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x0000024e   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x0000025a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0000025a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x0000025a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000264   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000266   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x00000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x00000268   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000026a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000026a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000026a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000270   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000270   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000274   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000274   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000027c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0000027e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0000027e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000282   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x00000288   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000002b8   Section        0  printf.o(.text)
    .text                                    0x000002d4   Section        0  sprintf.o(.text)
    .text                                    0x00000300   Section        0  rt_memclr.o(.text)
    .text                                    0x00000340   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x00000538   Section        0  heapauxi.o(.text)
    .text                                    0x00000540   Section        0  d2f.o(.text)
    _dadd1                                   0x000005bd   Thumb Code   290  daddsub.o(.text)
    .text                                    0x000005bc   Section        0  daddsub.o(.text)
    _dsub1                                   0x000006df   Thumb Code   470  daddsub.o(.text)
    .text                                    0x00000914   Section        0  ddiv.o(.text)
    .text                                    0x00000d5c   Section        0  dflti.o(.text)
    .text                                    0x00000db4   Section        0  dmul.o(.text)
    .text                                    0x00000ffc   Section        0  f2d.o(.text)
    .text                                    0x00001050   Section        0  ffixi.o(.text)
    .text                                    0x0000109c   Section        0  _printf_pad.o(.text)
    .text                                    0x000010ea   Section        0  _printf_truncate.o(.text)
    .text                                    0x0000110e   Section        0  _printf_str.o(.text)
    .text                                    0x00001160   Section        0  _printf_dec.o(.text)
    .text                                    0x000011cc   Section        0  _printf_charcount.o(.text)
    _printf_input_char                       0x000011f5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x000011f4   Section        0  _printf_char_common.o(.text)
    .text                                    0x00001224   Section        0  _sputc.o(.text)
    .text                                    0x00001230   Section        0  _printf_char_file.o(.text)
    .text                                    0x00001258   Section        0  _printf_wctomb.o(.text)
    .text                                    0x00001314   Section        0  _printf_longlong_dec.o(.text)
    _printf_longlong_oct_internal            0x00001385   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x00001384   Section        0  _printf_oct_int_ll.o(.text)
    _printf_hex_common                       0x000013f5   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x000013f4   Section        0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x0000148c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x00001614   Section        0  lludiv10.o(.text)
    .text                                    0x0000168e   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x00001741   Thumb Code   412  _printf_fp_dec.o(.text)
    .text                                    0x00001740   Section        0  _printf_fp_dec.o(.text)
    .text                                    0x00001b54   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x00001e2c   Section        0  _printf_char.o(.text)
    .text                                    0x00001e5a   Section        0  _printf_wchar.o(.text)
    .text                                    0x00001e88   Section        0  ferror.o(.text)
    .text                                    0x00001e90   Section        0  _c16rtomb.o(.text)
    .text                                    0x00001ed2   Section        0  rtudiv10.o(.text)
    .text                                    0x00001efc   Section        0  dcmpin.o(.text)
    .text                                    0x00001f9c   Section        8  libspace.o(.text)
    .text                                    0x00001fa4   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00001fe4   Section       16  rt_ctype_table.o(.text)
    .text                                    0x00001ff4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x00001ffc   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x00002084   Section        0  bigflt0.o(.text)
    .text                                    0x0000215c   Section        0  btod.o(.text)
    btod_internal_mul                        0x0000219d   Thumb Code   492  btod.o(.text)
    btod_internal_div                        0x00002389   Thumb Code   520  btod.o(.text)
    .text                                    0x000026dc   Section        0  exit.o(.text)
    .text                                    0x000026ec   Section        0  cmpret.o(.text)
    .text                                    0x0000271c   Section        0  dnan2.o(.text)
    .text                                    0x00002730   Section      176  strcmpv6m.o(.text)
    .text                                    0x000027e0   Section        0  retnan.o(.text)
    [Anonymous Symbol]                       0x0000283e   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x00002849   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00002848   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_GPIO_clearInterruptStatus             0x00002871   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00002870   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00002889   Thumb Code    24  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00002888   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.12_0                            0x000028a0   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x000028a5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000028a4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000028b9   Thumb Code    20  iic.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000028b8   Section        0  iic.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000028cd   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000028cc   Section        0  hw_lcd.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableHiZ                        0x000028e1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    [Anonymous Symbol]                       0x000028e0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    DL_GPIO_enableInterrupt                  0x000028f9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x000028f8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.38_0                            0x00002910   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00002915   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00002914   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enableOutput                     0x00002929   Thumb Code    20  iic.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00002928   Section        0  iic.o(.text.DL_GPIO_enableOutput)
    __arm_cp.4_0                             0x0000293c   Number         4  iic.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00002941   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00002940   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00002955   Thumb Code    20  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00002954   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.10_0                            0x00002968   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInput                 0x0000296d   Thumb Code    20  iic.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x0000296c   Section        0  iic.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalInputFeatures         0x00002981   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00002980   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.31_0                            0x000029ac   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x000029b1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000029b0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutput                0x000029c5   Thumb Code    20  iic.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000029c4   Section        0  iic.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutputFeatures        0x000029d9   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    [Anonymous Symbol]                       0x000029d8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    DL_GPIO_initPeripheralAnalogFunction     0x00002a05   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00002a04   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00002a19   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00002a18   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralInputFunctionFeatures 0x00002a31   Thumb Code    52  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    [Anonymous Symbol]                       0x00002a30   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    __arm_cp.27_0                            0x00002a64   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    DL_GPIO_initPeripheralOutputFunction     0x00002a69   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00002a68   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.25_0                            0x00002a80   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00002a85   Thumb Code    22  iic.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00002a84   Section        0  iic.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00002a9b   Thumb Code    22  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00002a9a   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00002ab1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00002ab0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00002ac1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00002ac0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.35_0                            0x00002ad8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00002add   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00002adc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00002af1   Thumb Code    20  iic.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00002af0   Section        0  iic.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00002b05   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00002b04   Section        0  hw_lcd.o(.text.DL_GPIO_setPins)
    __arm_cp.5_0                             0x00002b18   Number         4  hw_lcd.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00002b1d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00002b1c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    DL_I2C_enableAnalogGlitchFilter          0x00002b35   Thumb Code    24  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    [Anonymous Symbol]                       0x00002b34   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    DL_I2C_enableController                  0x00002b4d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    [Anonymous Symbol]                       0x00002b4c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    DL_I2C_enableControllerClockStretching   0x00002b61   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    [Anonymous Symbol]                       0x00002b60   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    __arm_cp.57_0                            0x00002b74   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    DL_I2C_enablePower                       0x00002b79   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00002b78   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    DL_I2C_reset                             0x00002b8d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_reset)
    [Anonymous Symbol]                       0x00002b8c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_reset)
    DL_I2C_resetControllerTransfer           0x00002b9d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    [Anonymous Symbol]                       0x00002b9c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    __arm_cp.53_0                            0x00002bac   Number         4  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    DL_I2C_setAnalogGlitchFilterPulseWidth   0x00002bb1   Thumb Code    38  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00002bb0   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00002bd6   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_I2C_setControllerRXFIFOThreshold      0x00002bfd   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    [Anonymous Symbol]                       0x00002bfc   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    DL_I2C_setControllerTXFIFOThreshold      0x00002c21   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    [Anonymous Symbol]                       0x00002c20   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    __arm_cp.55_0                            0x00002c44   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    DL_I2C_setTimerPeriod                    0x00002c49   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    [Anonymous Symbol]                       0x00002c48   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    __arm_cp.54_0                            0x00002c5c   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    DL_SPI_enable                            0x00002c61   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enable)
    [Anonymous Symbol]                       0x00002c60   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enable)
    __arm_cp.65_0                            0x00002c74   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enable)
    DL_SPI_enablePower                       0x00002c79   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00002c78   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    __arm_cp.23_0                            0x00002c8c   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00002c90   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x00002ccc   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x00002cd0   Number         4  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_isBusy                            0x00002cd5   Thumb Code    20  hw_lcd.o(.text.DL_SPI_isBusy)
    [Anonymous Symbol]                       0x00002cd4   Section        0  hw_lcd.o(.text.DL_SPI_isBusy)
    __arm_cp.2_0                             0x00002ce8   Number         4  hw_lcd.o(.text.DL_SPI_isBusy)
    DL_SPI_reset                             0x00002ced   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SPI_reset)
    [Anonymous Symbol]                       0x00002cec   Section        0  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.18_0                            0x00002cfc   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.18_1                            0x00002d00   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    DL_SPI_setBitRateSerialClockDivider      0x00002d05   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00002d04   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    __arm_cp.63_1                            0x00002d20   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00002d24   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SPI_setFIFOThreshold                  0x00002d39   Thumb Code    44  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    [Anonymous Symbol]                       0x00002d38   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    __arm_cp.64_0                            0x00002d64   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    DL_SPI_transmitData8                     0x00002d69   Thumb Code    22  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x00002d68   Section        0  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x00002d80   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00002e34   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00002e38   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00002e3c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00002e41   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00002e40   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00002e4d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00002e4c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.43_0                            0x00002e5c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x00002e61   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x00002e60   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_setBORThreshold                0x00002e71   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00002e70   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.39_0                            0x00002e84   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00002e89   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00002e88   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00002ea4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00002ef0   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setSYSOSCFreq                  0x00002ef5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00002ef4   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.41_0                            0x00002f0c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x00002f11   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00002f10   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00002f28   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00002f48   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00002f4c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x00002f51   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x00002f50   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x00002f5d   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00002f5c   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_0                            0x00002f78   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_1                            0x00002f7c   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_2                            0x00002f80   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    DL_Timer_enableClock                     0x00002f85   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00002f84   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.48_0                            0x00002f94   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00002f99   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00002f98   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00002fb1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00002fb0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.20_0                            0x00002fc4   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    DL_Timer_getPendingInterrupt             0x00002fc9   Thumb Code    18  empty.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00002fc8   Section        0  empty.o(.text.DL_Timer_getPendingInterrupt)
    DL_Timer_getPendingInterrupt             0x00002fdb   Thumb Code    18  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00002fda   Section        0  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00002fec   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x000030e0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x000030e4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x000030e8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x000030ec   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x000030f0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x000030f4   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000031d4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x000031d8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x000031dc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x000031e1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x000031e0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x000031f1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x000031f0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00003204   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x0000321c   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00003220   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00003234   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00003238   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00003244   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00003248   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00003260   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x00003265   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x00003264   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.47_0                            0x00003298   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.47_1                            0x0000329c   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x000032a1   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x000032a0   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableInterrupt                  0x000032b9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x000032b8   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.61_0                            0x000032d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x000032d5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x000032d4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.22_0                            0x000032e8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x000032ed   Thumb Code    18  empty.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x000032ec   Section        0  empty.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00003300   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00003340   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00003344   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00003349   Thumb Code    20  usart.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00003348   Section        0  usart.o(.text.DL_UART_isBusy)
    __arm_cp.1_0                             0x0000335c   Number         4  usart.o(.text.DL_UART_isBusy)
    DL_UART_receiveData                      0x00003361   Thumb Code    16  empty.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x00003360   Section        0  empty.o(.text.DL_UART_receiveData)
    __arm_cp.7_0                             0x00003370   Number         4  empty.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x00003375   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x00003374   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.17_0                            0x00003384   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.17_1                            0x00003388   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x0000338d   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x0000338c   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_0                            0x000033c8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_1                            0x000033cc   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_2                            0x000033d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_3                            0x000033d4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000033d8   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000033eb   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000033ea   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_transmitData                     0x00003409   Thumb Code    22  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00003408   Section        0  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00003420   Section        0  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_1                             0x00003544   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_2                             0x00003548   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_3                             0x0000354c   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00003550   Section        0  iic.o(.text.I2C_WaitAck)
    [Anonymous Symbol]                       0x000035fc   Section        0  iic.o(.text.IIC_Send_Ack)
    [Anonymous Symbol]                       0x00003674   Section        0  iic.o(.text.IIC_Start)
    [Anonymous Symbol]                       0x000036cc   Section        0  iic.o(.text.IIC_Stop)
    [Anonymous Symbol]                       0x00003718   Section        0  hw_lcd.o(.text.LCD_WR_DATA8)
    [Anonymous Symbol]                       0x0000372c   Section        0  hw_lcd.o(.text.LCD_WR_REG)
    [Anonymous Symbol]                       0x00003758   Section        0  hw_lcd.o(.text.LCD_Writ_Bus)
    __arm_cp.3_0                             0x00003784   Number         4  hw_lcd.o(.text.LCD_Writ_Bus)
    [Anonymous Symbol]                       0x00003788   Section        0  iic.o(.text.Read_Byte)
    __arm_cp.13_0                            0x0000381c   Number         4  iic.o(.text.Read_Byte)
    [Anonymous Symbol]                       0x00003820   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x000039e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000039ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000039f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x000039f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x000039f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x000039fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00003a00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00003a04   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.8_0                             0x00003a54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00003a58   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_0                             0x00003adc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_2                             0x00003ae0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00003ae4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_0                             0x00003b44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_2                             0x00003b48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    [Anonymous Symbol]                       0x00003b4c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_0                            0x00003b80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_2                            0x00003b84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    [Anonymous Symbol]                       0x00003b88   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00003bdc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00003be0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00003bf0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x00003c18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x00003c1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00003c20   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_0                             0x00003c50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_2                             0x00003c54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    [Anonymous Symbol]                       0x00003c58   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_0                             0x00003c94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_2                             0x00003c98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00003c9c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00003ce4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00003ce8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00003cec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00003cf0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00003cf4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00003d80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00003d84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00003d88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003d8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00003d90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003d94   Section        0  iic.o(.text.Send_Byte)
    __arm_cp.12_0                            0x00003e24   Number         4  iic.o(.text.Send_Byte)
    [Anonymous Symbol]                       0x00003e28   Section        0  hw_timer.o(.text.TIMA0_IRQHandler)
    __arm_cp.3_0                             0x00003e44   Number         4  hw_timer.o(.text.TIMA0_IRQHandler)
    [Anonymous Symbol]                       0x00003e48   Section        0  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_0                             0x00003ed4   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_1                             0x00003ed8   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_2                             0x00003edc   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_3                             0x00003ee0   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_4                             0x00003ee4   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_5                             0x00003ee8   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_6                             0x00003eec   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_7                             0x00003ef0   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_8                             0x00003ef4   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.8_9                             0x00003ef8   Number         4  empty.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00003efc   Section        0  empty.o(.text.UART0_IRQHandler)
    __arm_cp.5_0                             0x00003f24   Number         4  empty.o(.text.UART0_IRQHandler)
    __arm_cp.5_1                             0x00003f28   Number         4  empty.o(.text.UART0_IRQHandler)
    __NVIC_ClearPendingIRQ                   0x00003f2d   Thumb Code    40  empty.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003f2c   Section        0  empty.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00003f55   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003f54   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00003f7d   Thumb Code    40  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003f7c   Section        0  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x00003fa4   Number         4  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x00003fa9   Thumb Code    40  empty.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003fa8   Section        0  empty.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00003fd1   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003fd0   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00003ff9   Thumb Code    40  hw_timer.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003ff8   Section        0  hw_timer.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x00004020   Number         4  hw_timer.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00004025   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00004024   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.46_0                            0x000040a0   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.46_1                            0x000040a4   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000040a8   Section        0  empty.o(.text._sys_exit)
    [Anonymous Symbol]                       0x000040b0   Section        0  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x000040c8   Section        0  time.o(.text.delay_us)
    __arm_cp.0_0                             0x00004134   Number         4  time.o(.text.delay_us)
    __arm_cp.0_1                             0x00004138   Number         4  time.o(.text.delay_us)
    [Anonymous Symbol]                       0x0000413c   Section        0  bsp_motor_hallencoder.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00004154   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_L)
    __arm_cp.7_0                             0x0000416c   Number         4  bsp_motor_hallencoder.o(.text.encoder_update_L)
    [Anonymous Symbol]                       0x00004170   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_R)
    __arm_cp.8_0                             0x00004188   Number         4  bsp_motor_hallencoder.o(.text.encoder_update_R)
    [Anonymous Symbol]                       0x0000418c   Section        0  usart.o(.text.fputc)
    __arm_cp.4_0                             0x000041b4   Number         4  usart.o(.text.fputc)
    [Anonymous Symbol]                       0x000041b8   Section        0  iic.o(.text.get_angle)
    __arm_cp.15_0                            0x00004374   Number         4  iic.o(.text.get_angle)
    __arm_cp.15_1                            0x00004378   Number         4  iic.o(.text.get_angle)
    __arm_cp.15_2                            0x0000437c   Number         4  iic.o(.text.get_angle)
    __arm_cp.15_3                            0x00004380   Number         4  iic.o(.text.get_angle)
    __arm_cp.15_4                            0x00004384   Number         4  iic.o(.text.get_angle)
    __arm_cp.15_5                            0x00004388   Number         4  iic.o(.text.get_angle)
    [Anonymous Symbol]                       0x0000438c   Section        0  iic.o(.text.jy61pInit)
    __arm_cp.0_0                             0x00004428   Number         4  iic.o(.text.jy61pInit)
    __arm_cp.0_1                             0x0000442c   Number         4  iic.o(.text.jy61pInit)
    [Anonymous Symbol]                       0x00004430   Section        0  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_0                            0x000045f0   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_1                            0x000045f4   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_2                            0x000045f8   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_3                            0x000045fc   Number         4  hw_lcd.o(.text.lcd_init)
    [Anonymous Symbol]                       0x00004600   Section        0  empty.o(.text.main)
    __arm_cp.1_0                             0x000046a8   Number         4  empty.o(.text.main)
    __arm_cp.1_1                             0x000046ac   Number         4  empty.o(.text.main)
    __arm_cp.1_2                             0x000046b0   Number         4  empty.o(.text.main)
    __arm_cp.1_3                             0x000046b4   Number         4  empty.o(.text.main)
    __arm_cp.1_4                             0x000046b8   Number         4  empty.o(.text.main)
    __arm_cp.1_5                             0x000046bc   Number         4  empty.o(.text.main)
    __arm_cp.1_6                             0x000046c0   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x000046c4   Section        0  iic.o(.text.readDataJy61p)
    [Anonymous Symbol]                       0x00004798   Section        0  hw_lcd.o(.text.spi_write_bus)
    __arm_cp.0_0                             0x000047c0   Number         4  hw_lcd.o(.text.spi_write_bus)
    [Anonymous Symbol]                       0x000047c4   Section        0  hw_timer.o(.text.timer_init)
    [Anonymous Symbol]                       0x000047dc   Section        0  usart.o(.text.uart0_send_char)
    __arm_cp.0_0                             0x00004804   Number         4  usart.o(.text.uart0_send_char)
    [Anonymous Symbol]                       0x00004808   Section        0  usart.o(.text.uart0_send_string)
    [Anonymous Symbol]                       0x00004844   Section        0  iic.o(.text.writeDataJy61p)
    i.__ARM_common_ll_muluu                  0x000048dc   Section        0  btod.o(i.__ARM_common_ll_muluu)
    i.__ARM_fpclassify                       0x0000490c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._dgeq                                  0x00004938   Section        0  dcmp.o(i._dgeq)
    i._dleq                                  0x0000494e   Section        0  dcmp.o(i._dleq)
    i._is_digit                              0x00004968   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x00004978   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x000049a4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dgeqf                              0x000049d0   Section      100  dgef.o(x$fpl$dgeqf)
    x$fpl$dleqf                              0x00004a34   Section      100  dlef.o(x$fpl$dleqf)
    x$fpl$printf1                            0x00004a98   Section       16  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x00004aa8   Section       16  printf2.o(x$fpl$printf2)
    ddiv_reciptbl                            0x00004ab8   Data         128  ddiv.o(.constdata)
    .constdata                               0x00004ab8   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x00004ab8   Section        0  usenofp.o(x$fpl$usenofp)
    initial_mbstate                          0x00004b38   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x00004b38   Section        8  _printf_wctomb.o(.constdata)
    uc_hextab                                0x00004b40   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x00004b40   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x00004b54   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    maptable                                 0x00004b68   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x00004b68   Section       17  __printf_flags_ss_wp.o(.constdata)
    lc_hextab                                0x00004b79   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x00004b79   Section       38  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x00004b8c   Data          19  _printf_fp_hex.o(.constdata)
    tenpwrs_x                                0x00004ba0   Data          60  bigflt0.o(.constdata)
    .constdata                               0x00004ba0   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x00004bdc   Data          64  bigflt0.o(.constdata)
    gI2C_0ClockConfig                        0x00004c34   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x00004c34   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x00004c36   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x00004c36   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x00004c3c   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x00004c3c   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gPWM_LEDClockConfig                      0x00004c44   Data           3  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    [Anonymous Symbol]                       0x00004c44   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    gPWM_LEDConfig                           0x00004c48   Data           8  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    [Anonymous Symbol]                       0x00004c48   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    gSPI_LCD_clockConfig                     0x00004c50   Data           2  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    [Anonymous Symbol]                       0x00004c50   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    gSPI_LCD_config                          0x00004c52   Data          10  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    [Anonymous Symbol]                       0x00004c52   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    gSYSPLLConfig                            0x00004c5c   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00004c5c   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x00004c84   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00004c84   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00004c88   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00004c88   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gTIMER_TICKClockConfig                   0x00004c9c   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    [Anonymous Symbol]                       0x00004c9c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    gTIMER_TICKTimerConfig                   0x00004ca0   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    [Anonymous Symbol]                       0x00004ca0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    gUART_0ClockConfig                       0x00004cb4   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00004cb4   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00004cb6   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00004cb6   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00004cc0   Section        0  empty.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00004d59   Section        0  iic.o(.rodata.str1.1)
    locale$$data                             0x00004d88   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x00004d8c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x00004d94   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x00004da0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x00004da2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x00004da3   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x00004da4   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x00004da4   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x00004da8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x00004db0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x00004eb4   Data           0  lc_ctype_c.o(locale$$data)
    TIMG0_IRQHandler.timer_count             0x20200000   Data           4  empty.o(.data.TIMG0_IRQHandler.timer_count)
    [Anonymous Symbol]                       0x20200000   Section        0  empty.o(.data.TIMG0_IRQHandler.timer_count)
    .bss                                     0x20200008   Section       96  libspace.o(.bss)
    Gyro_Structure                           0x20200068   Data          12  iic.o(.bss.Gyro_Structure)
    [Anonymous Symbol]                       0x20200068   Section        0  iic.o(.bss.Gyro_Structure)
    motor_encoder_L                          0x202002f8   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    [Anonymous Symbol]                       0x202002f8   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    motor_encoder_R                          0x20200308   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    [Anonymous Symbol]                       0x20200308   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    Heap_Mem                                 0x20200420   Data        1024  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200420   Section     1024  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200820   Data        1024  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200820   Section     1024  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200c20   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_n                                0x00000167   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_p                                0x00000171   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0000017b   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x00000185   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x0000018f   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x00000199   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x000001a3   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x000001ad   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x000001b7   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x000001c1   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x000001cb   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x000001d5   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x000001df   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x000001e9   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x000001f3   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x000001fd   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x00000207   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x00000211   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x0000021b   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x00000225   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0000022f   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x00000239   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x00000243   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x00000247   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x00000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x0000024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x0000024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x0000025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x0000025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x0000025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x00000267   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x00000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000026b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000026b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000026b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000271   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000271   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000027d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0000027f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0000027f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000283   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x00000289   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x0000028d   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x0000028f   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000291   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000293   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x00000295   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x00000297   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x00000297   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x00000299   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    printf                                   0x000002b9   Thumb Code    22  printf.o(.text)
    sprintf                                  0x000002d5   Thumb Code    40  sprintf.o(.text)
    _memset_w                                0x00000301   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x0000031b   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x00000339   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x00000339   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0000033d   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x0000033d   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x0000033d   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidivmod                         0x00000341   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x0000035d   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x00000539   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000053b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0000053d   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x00000541   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x00000541   Thumb Code   120  d2f.o(.text)
    __aeabi_dadd                             0x000008b5   Thumb Code     0  daddsub.o(.text)
    _dadd                                    0x000008b5   Thumb Code    26  daddsub.o(.text)
    __aeabi_dsub                             0x000008cf   Thumb Code     0  daddsub.o(.text)
    _dsub                                    0x000008cf   Thumb Code    22  daddsub.o(.text)
    __aeabi_drsub                            0x000008e5   Thumb Code     0  daddsub.o(.text)
    _drsb                                    0x000008e5   Thumb Code    28  daddsub.o(.text)
    __aeabi_ddiv                             0x00000915   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000915   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000d45   Thumb Code    20  ddiv.o(.text)
    __aeabi_i2d_normalise                    0x00000d5d   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000d9f   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000d9f   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000daf   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000daf   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x00000db5   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x00000db5   Thumb Code   558  dmul.o(.text)
    __aeabi_f2d                              0x00000ffd   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000ffd   Thumb Code    80  f2d.o(.text)
    __aeabi_f2iz                             0x00001051   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x00001051   Thumb Code    76  ffixi.o(.text)
    _printf_pre_padding                      0x0000109d   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x000010c9   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x000010eb   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x000010fd   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0000110f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x00001161   Thumb Code    90  _printf_dec.o(.text)
    _printf_charcount                        0x000011cd   Thumb Code    38  _printf_charcount.o(.text)
    _printf_char_common                      0x000011ff   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x00001225   Thumb Code    10  _sputc.o(.text)
    _printf_char_file                        0x00001231   Thumb Code    34  _printf_char_file.o(.text)
    _printf_wctomb                           0x00001259   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x00001315   Thumb Code    94  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x00001385   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x000013c9   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x000013e1   Thumb Code    10  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x000013f5   Thumb Code    88  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x0000144d   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x00001469   Thumb Code    10  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x00001473   Thumb Code    22  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x0000148d   Thumb Code   386  __printf_flags_ss_wp.o(.text)
    _ll_udiv10                               0x00001615   Thumb Code   122  lludiv10.o(.text)
    _printf_int_common                       0x0000168f   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x000018dd   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x00001b55   Thumb Code   718  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x00001e2d   Thumb Code    22  _printf_char.o(.text)
    _printf_char                             0x00001e43   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x00001e53   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x00001e5b   Thumb Code    22  _printf_wchar.o(.text)
    _printf_wchar                            0x00001e71   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x00001e81   Thumb Code     8  _printf_wchar.o(.text)
    ferror                                   0x00001e89   Thumb Code     8  ferror.o(.text)
    _c16rtomb                                0x00001e91   Thumb Code    66  _c16rtomb.o(.text)
    _wcrtomb                                 0x00001e91   Thumb Code     0  _c16rtomb.o(.text)
    __rt_udiv10                              0x00001ed3   Thumb Code    40  rtudiv10.o(.text)
    __fpl_dcmp_InfNaN                        0x00001efd   Thumb Code   154  dcmpin.o(.text)
    __user_libspace                          0x00001f9d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00001f9d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00001f9d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x00001fa5   Thumb Code    62  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x00001fe5   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x00001ff5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x00001ffd   Thumb Code   120  _printf_fp_infnan.o(.text)
    _btod_etento                             0x00002085   Thumb Code   210  bigflt0.o(.text)
    _btod_d2e                                0x0000215d   Thumb Code    64  btod.o(.text)
    _btod_emul                               0x00002591   Thumb Code    28  btod.o(.text)
    _btod_emuld                              0x000025ad   Thumb Code   144  btod.o(.text)
    _btod_ediv                               0x0000263d   Thumb Code    26  btod.o(.text)
    _btod_edivd                              0x00002657   Thumb Code   124  btod.o(.text)
    exit                                     0x000026dd   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x000026ed   Thumb Code    46  cmpret.o(.text)
    __fpl_dcheck_NaN2                        0x0000271d   Thumb Code    14  dnan2.o(.text)
    strcmp                                   0x00002731   Thumb Code   176  strcmpv6m.o(.text)
    __fpl_return_NaN                         0x000027e1   Thumb Code    94  retnan.o(.text)
    DL_Common_delayCycles                    0x0000283f   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_setClockConfig                    0x00002bd7   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x00002c91   Thumb Code    68  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00002d25   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x00002d81   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00002ea5   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00002f29   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00002fed   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x000030f5   Thumb Code   236  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00003205   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00003221   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00003239   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00003249   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00003301   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000033d9   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x00003421   Thumb Code   292  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    I2C_WaitAck                              0x00003551   Thumb Code   172  iic.o(.text.I2C_WaitAck)
    IIC_Send_Ack                             0x000035fd   Thumb Code   120  iic.o(.text.IIC_Send_Ack)
    IIC_Start                                0x00003675   Thumb Code    88  iic.o(.text.IIC_Start)
    IIC_Stop                                 0x000036cd   Thumb Code    76  iic.o(.text.IIC_Stop)
    LCD_WR_DATA8                             0x00003719   Thumb Code    20  hw_lcd.o(.text.LCD_WR_DATA8)
    LCD_WR_REG                               0x0000372d   Thumb Code    44  hw_lcd.o(.text.LCD_WR_REG)
    LCD_Writ_Bus                             0x00003759   Thumb Code    44  hw_lcd.o(.text.LCD_Writ_Bus)
    Read_Byte                                0x00003789   Thumb Code   148  iic.o(.text.Read_Byte)
    SYSCFG_DL_GPIO_init                      0x00003821   Thumb Code   456  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00003a05   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00003a59   Thumb Code   132  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_PWM_LED_init                   0x00003ae5   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    SYSCFG_DL_SPI_LCD_init                   0x00003b4d   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    SYSCFG_DL_SYSCTL_init                    0x00003b89   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00003be1   Thumb Code    14  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00003bf1   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_TIMER_TICK_init                0x00003c21   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    SYSCFG_DL_UART_0_init                    0x00003c59   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00003c9d   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00003cf5   Thumb Code   140  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Send_Byte                                0x00003d95   Thumb Code   144  iic.o(.text.Send_Byte)
    TIMA0_IRQHandler                         0x00003e29   Thumb Code    28  hw_timer.o(.text.TIMA0_IRQHandler)
    TIMG0_IRQHandler                         0x00003e49   Thumb Code   140  empty.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x00003efd   Thumb Code    40  empty.o(.text.UART0_IRQHandler)
    _sys_exit                                0x000040a9   Thumb Code     8  empty.o(.text._sys_exit)
    delay_ms                                 0x000040b1   Thumb Code    22  time.o(.text.delay_ms)
    delay_us                                 0x000040c9   Thumb Code   108  time.o(.text.delay_us)
    encoder_init                             0x0000413d   Thumb Code    22  bsp_motor_hallencoder.o(.text.encoder_init)
    encoder_update_L                         0x00004155   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_L)
    encoder_update_R                         0x00004171   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_R)
    fputc                                    0x0000418d   Thumb Code    40  usart.o(.text.fputc)
    get_angle                                0x000041b9   Thumb Code   444  iic.o(.text.get_angle)
    jy61pInit                                0x0000438d   Thumb Code   156  iic.o(.text.jy61pInit)
    lcd_init                                 0x00004431   Thumb Code   448  hw_lcd.o(.text.lcd_init)
    main                                     0x00004601   Thumb Code   168  empty.o(.text.main)
    readDataJy61p                            0x000046c5   Thumb Code   212  iic.o(.text.readDataJy61p)
    spi_write_bus                            0x00004799   Thumb Code    40  hw_lcd.o(.text.spi_write_bus)
    timer_init                               0x000047c5   Thumb Code    22  hw_timer.o(.text.timer_init)
    uart0_send_char                          0x000047dd   Thumb Code    40  usart.o(.text.uart0_send_char)
    uart0_send_string                        0x00004809   Thumb Code    60  usart.o(.text.uart0_send_string)
    writeDataJy61p                           0x00004845   Thumb Code   152  iic.o(.text.writeDataJy61p)
    __ARM_common_ll_muluu                    0x000048dd   Thumb Code    48  btod.o(i.__ARM_common_ll_muluu)
    __ARM_fpclassify                         0x0000490d   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __aeabi_dcmpge                           0x00004939   Thumb Code     0  dcmp.o(i._dgeq)
    _dgeq                                    0x00004939   Thumb Code    22  dcmp.o(i._dgeq)
    __aeabi_dcmple                           0x0000494f   Thumb Code     0  dcmp.o(i._dleq)
    _dleq                                    0x0000494f   Thumb Code    26  dcmp.o(i._dleq)
    _is_digit                                0x00004969   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x00004979   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x000049a5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _dcmpge                                  0x000049d1   Thumb Code    94  dgef.o(x$fpl$dgeqf)
    __aeabi_cdcmple                          0x00004a35   Thumb Code     0  dlef.o(x$fpl$dleqf)
    _dcmple                                  0x00004a35   Thumb Code    94  dlef.o(x$fpl$dleqf)
    _printf_fp_dec                           0x00004a99   Thumb Code    16  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x00004aa9   Thumb Code    16  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x00004ab8   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x00004d68   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00004d88   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x00004db1   Data           0  lc_ctype_c.o(locale$$data)
    __libspace_start                         0x20200008   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200068   Data           0  libspace.o(.bss)
    PID_flash_time                           0x20200074   Data           1  empty.o(.bss.PID_flash_time)
    __stdout                                 0x20200078   Data          84  empty.o(.bss.__stdout)
    bmq_flash_time                           0x202000cc   Data           1  empty.o(.bss.bmq_flash_time)
    gPWM_0Backup                             0x202000d0   Data         160  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gPWM_LEDBackup                           0x20200170   Data         160  ti_msp_dl_config.o(.bss.gPWM_LEDBackup)
    gSPI_LCDBackup                           0x20200210   Data          40  ti_msp_dl_config.o(.bss.gSPI_LCDBackup)
    gTIMER_TICKBackup                        0x20200238   Data         188  ti_msp_dl_config.o(.bss.gTIMER_TICKBackup)
    led_flash_time                           0x202002f4   Data           1  empty.o(.bss.led_flash_time)
    rx_buff                                  0x20200318   Data         256  empty.o(.bss.rx_buff)
    uart_data                                0x20200418   Data           1  empty.o(.bss.uart_data)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00004ec0, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00004eb4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           55    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          883  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1311    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO         1315    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO         1312    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO         1317    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO         1035    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO         1024    .ARM.Collect$$_printf_percent$$00000001  c_p.l(_printf_n.o)
    0x00000170   0x00000170   0x0000000a   Code   RO         1026    .ARM.Collect$$_printf_percent$$00000002  c_p.l(_printf_p.o)
    0x0000017a   0x0000017a   0x0000000a   Code   RO         1031    .ARM.Collect$$_printf_percent$$00000003  c_p.l(_printf_f.o)
    0x00000184   0x00000184   0x0000000a   Code   RO         1032    .ARM.Collect$$_printf_percent$$00000004  c_p.l(_printf_e.o)
    0x0000018e   0x0000018e   0x0000000a   Code   RO         1033    .ARM.Collect$$_printf_percent$$00000005  c_p.l(_printf_g.o)
    0x00000198   0x00000198   0x0000000a   Code   RO         1034    .ARM.Collect$$_printf_percent$$00000006  c_p.l(_printf_a.o)
    0x000001a2   0x000001a2   0x0000000a   Code   RO         1039    .ARM.Collect$$_printf_percent$$00000007  c_p.l(_printf_ll.o)
    0x000001ac   0x000001ac   0x0000000a   Code   RO         1028    .ARM.Collect$$_printf_percent$$00000008  c_p.l(_printf_i.o)
    0x000001b6   0x000001b6   0x0000000a   Code   RO         1029    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x000001c0   0x000001c0   0x0000000a   Code   RO         1030    .ARM.Collect$$_printf_percent$$0000000A  c_p.l(_printf_u.o)
    0x000001ca   0x000001ca   0x0000000a   Code   RO         1027    .ARM.Collect$$_printf_percent$$0000000B  c_p.l(_printf_o.o)
    0x000001d4   0x000001d4   0x0000000a   Code   RO         1025    .ARM.Collect$$_printf_percent$$0000000C  c_p.l(_printf_x.o)
    0x000001de   0x000001de   0x0000000a   Code   RO         1036    .ARM.Collect$$_printf_percent$$0000000D  c_p.l(_printf_lli.o)
    0x000001e8   0x000001e8   0x0000000a   Code   RO         1037    .ARM.Collect$$_printf_percent$$0000000E  c_p.l(_printf_lld.o)
    0x000001f2   0x000001f2   0x0000000a   Code   RO         1038    .ARM.Collect$$_printf_percent$$0000000F  c_p.l(_printf_llu.o)
    0x000001fc   0x000001fc   0x0000000a   Code   RO         1043    .ARM.Collect$$_printf_percent$$00000010  c_p.l(_printf_llo.o)
    0x00000206   0x00000206   0x0000000a   Code   RO         1044    .ARM.Collect$$_printf_percent$$00000011  c_p.l(_printf_llx.o)
    0x00000210   0x00000210   0x0000000a   Code   RO         1040    .ARM.Collect$$_printf_percent$$00000012  c_p.l(_printf_l.o)
    0x0000021a   0x0000021a   0x0000000a   Code   RO         1022    .ARM.Collect$$_printf_percent$$00000013  c_p.l(_printf_c.o)
    0x00000224   0x00000224   0x0000000a   Code   RO         1023    .ARM.Collect$$_printf_percent$$00000014  c_p.l(_printf_s.o)
    0x0000022e   0x0000022e   0x0000000a   Code   RO         1041    .ARM.Collect$$_printf_percent$$00000015  c_p.l(_printf_lc.o)
    0x00000238   0x00000238   0x0000000a   Code   RO         1042    .ARM.Collect$$_printf_percent$$00000016  c_p.l(_printf_ls.o)
    0x00000242   0x00000242   0x00000004   Code   RO         1107    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x00000246   0x00000246   0x00000002   Code   RO         1158    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1186    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1188    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1190    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1193    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1195    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000000   Code   RO         1197    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000248   0x00000248   0x00000006   Code   RO         1198    .ARM.Collect$$libinit$$00000011  c_p.l(libinit2.o)
    0x0000024e   0x0000024e   0x00000000   Code   RO         1200    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x0000024e   0x0000024e   0x0000000c   Code   RO         1201    .ARM.Collect$$libinit$$00000014  c_p.l(libinit2.o)
    0x0000025a   0x0000025a   0x00000000   Code   RO         1202    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x0000025a   0x0000025a   0x00000000   Code   RO         1204    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x0000025a   0x0000025a   0x0000000a   Code   RO         1205    .ARM.Collect$$libinit$$00000018  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1206    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1208    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1210    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1212    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1214    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1216    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1218    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1220    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1224    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1226    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1228    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000000   Code   RO         1230    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000264   0x00000264   0x00000002   Code   RO         1231    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000266   0x00000266   0x00000002   Code   RO         1263    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1294    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1296    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1299    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1302    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1304    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000000   Code   RO         1307    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x00000268   0x00000268   0x00000002   Code   RO         1308    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000026a   0x0000026a   0x00000000   Code   RO          945    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000026a   0x0000026a   0x00000000   Code   RO         1069    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000026a   0x0000026a   0x00000006   Code   RO         1081    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000270   0x00000270   0x00000000   Code   RO         1071    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000270   0x00000270   0x00000004   Code   RO         1072    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000274   0x00000274   0x00000000   Code   RO         1074    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000274   0x00000274   0x00000008   Code   RO         1075    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000027c   0x0000027c   0x00000002   Code   RO         1170    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x0000027e   0x0000027e   0x00000000   Code   RO         1235    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x0000027e   0x0000027e   0x00000004   Code   RO         1236    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000282   0x00000282   0x00000006   Code   RO         1237    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x00000288   0x00000288   0x00000030   Code   RO           56    .text               startup_mspm0g350x_uvision.o
    0x000002b8   0x000002b8   0x0000001c   Code   RO          863    .text               c_p.l(printf.o)
    0x000002d4   0x000002d4   0x0000002c   Code   RO          865    .text               c_p.l(sprintf.o)
    0x00000300   0x00000300   0x00000040   Code   RO          871    .text               c_p.l(rt_memclr.o)
    0x00000340   0x00000340   0x000001f8   Code   RO          873    .text               c_p.l(aeabi_sdivfast.o)
    0x00000538   0x00000538   0x00000006   Code   RO          881    .text               c_p.l(heapauxi.o)
    0x0000053e   0x0000053e   0x00000002   PAD
    0x00000540   0x00000540   0x0000007c   Code   RO          885    .text               fz_ps.l(d2f.o)
    0x000005bc   0x000005bc   0x00000358   Code   RO          887    .text               fz_ps.l(daddsub.o)
    0x00000914   0x00000914   0x00000448   Code   RO          901    .text               fz_ps.l(ddiv.o)
    0x00000d5c   0x00000d5c   0x00000058   Code   RO          906    .text               fz_ps.l(dflti.o)
    0x00000db4   0x00000db4   0x00000248   Code   RO          908    .text               fz_ps.l(dmul.o)
    0x00000ffc   0x00000ffc   0x00000054   Code   RO          910    .text               fz_ps.l(f2d.o)
    0x00001050   0x00001050   0x0000004c   Code   RO          933    .text               fz_ps.l(ffixi.o)
    0x0000109c   0x0000109c   0x0000004e   Code   RO          954    .text               c_p.l(_printf_pad.o)
    0x000010ea   0x000010ea   0x00000024   Code   RO          956    .text               c_p.l(_printf_truncate.o)
    0x0000110e   0x0000110e   0x00000052   Code   RO          958    .text               c_p.l(_printf_str.o)
    0x00001160   0x00001160   0x0000006c   Code   RO          960    .text               c_p.l(_printf_dec.o)
    0x000011cc   0x000011cc   0x00000026   Code   RO          962    .text               c_p.l(_printf_charcount.o)
    0x000011f2   0x000011f2   0x00000002   PAD
    0x000011f4   0x000011f4   0x00000030   Code   RO          964    .text               c_p.l(_printf_char_common.o)
    0x00001224   0x00001224   0x0000000a   Code   RO          966    .text               c_p.l(_sputc.o)
    0x0000122e   0x0000122e   0x00000002   PAD
    0x00001230   0x00001230   0x00000028   Code   RO          968    .text               c_p.l(_printf_char_file.o)
    0x00001258   0x00001258   0x000000bc   Code   RO          970    .text               c_p.l(_printf_wctomb.o)
    0x00001314   0x00001314   0x00000070   Code   RO          973    .text               c_p.l(_printf_longlong_dec.o)
    0x00001384   0x00001384   0x00000070   Code   RO          979    .text               c_p.l(_printf_oct_int_ll.o)
    0x000013f4   0x000013f4   0x00000098   Code   RO          999    .text               c_p.l(_printf_hex_int_ll_ptr.o)
    0x0000148c   0x0000148c   0x00000188   Code   RO         1019    .text               c_p.l(__printf_flags_ss_wp.o)
    0x00001614   0x00001614   0x0000007a   Code   RO         1090    .text               c_p.l(lludiv10.o)
    0x0000168e   0x0000168e   0x000000b0   Code   RO         1092    .text               c_p.l(_printf_intcommon.o)
    0x0000173e   0x0000173e   0x00000002   PAD
    0x00001740   0x00001740   0x00000414   Code   RO         1094    .text               c_p.l(_printf_fp_dec.o)
    0x00001b54   0x00001b54   0x000002d8   Code   RO         1098    .text               c_p.l(_printf_fp_hex.o)
    0x00001e2c   0x00001e2c   0x0000002e   Code   RO         1103    .text               c_p.l(_printf_char.o)
    0x00001e5a   0x00001e5a   0x0000002e   Code   RO         1105    .text               c_p.l(_printf_wchar.o)
    0x00001e88   0x00001e88   0x00000008   Code   RO         1108    .text               c_p.l(ferror.o)
    0x00001e90   0x00001e90   0x00000042   Code   RO         1112    .text               c_p.l(_c16rtomb.o)
    0x00001ed2   0x00001ed2   0x00000028   Code   RO         1114    .text               c_p.l(rtudiv10.o)
    0x00001efa   0x00001efa   0x00000002   PAD
    0x00001efc   0x00001efc   0x000000a0   Code   RO         1116    .text               fz_ps.l(dcmpin.o)
    0x00001f9c   0x00001f9c   0x00000008   Code   RO         1120    .text               c_p.l(libspace.o)
    0x00001fa4   0x00001fa4   0x0000003e   Code   RO         1123    .text               c_p.l(sys_stackheap_outer.o)
    0x00001fe2   0x00001fe2   0x00000002   PAD
    0x00001fe4   0x00001fe4   0x00000010   Code   RO         1126    .text               c_p.l(rt_ctype_table.o)
    0x00001ff4   0x00001ff4   0x00000008   Code   RO         1131    .text               c_p.l(rt_locale_intlibspace.o)
    0x00001ffc   0x00001ffc   0x00000088   Code   RO         1133    .text               c_p.l(_printf_fp_infnan.o)
    0x00002084   0x00002084   0x000000d8   Code   RO         1135    .text               c_p.l(bigflt0.o)
    0x0000215c   0x0000215c   0x00000580   Code   RO         1138    .text               c_p.l(btod.o)
    0x000026dc   0x000026dc   0x00000010   Code   RO         1147    .text               c_p.l(exit.o)
    0x000026ec   0x000026ec   0x0000002e   Code   RO         1159    .text               fz_ps.l(cmpret.o)
    0x0000271a   0x0000271a   0x00000002   PAD
    0x0000271c   0x0000271c   0x00000014   Code   RO         1161    .text               fz_ps.l(dnan2.o)
    0x00002730   0x00002730   0x000000b0   Code   RO         1183    .text               c_p.l(strcmpv6m.o)
    0x000027e0   0x000027e0   0x0000005e   Code   RO         1232    .text               fz_ps.l(retnan.o)
    0x0000283e   0x0000283e   0x0000000a   Code   RO          594    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00002848   0x00002848   0x00000028   Code   RO          199    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00002870   0x00002870   0x00000018   Code   RO          137    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00002888   0x00002888   0x0000001c   Code   RO          465    .text.DL_GPIO_clearInterruptStatus  bsp_motor_hallencoder.o
    0x000028a4   0x000028a4   0x00000014   Code   RO          131    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x000028b8   0x000028b8   0x00000014   Code   RO          291    .text.DL_GPIO_clearPins  iic.o
    0x000028cc   0x000028cc   0x00000014   Code   RO          332    .text.DL_GPIO_clearPins  hw_lcd.o
    0x000028e0   0x000028e0   0x00000018   Code   RO          119    .text.DL_GPIO_enableHiZ  ti_msp_dl_config.o
    0x000028f8   0x000028f8   0x0000001c   Code   RO          139    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00002914   0x00002914   0x00000014   Code   RO          115    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00002928   0x00002928   0x00000018   Code   RO          289    .text.DL_GPIO_enableOutput  iic.o
    0x00002940   0x00002940   0x00000014   Code   RO          101    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00002954   0x00002954   0x00000018   Code   RO          461    .text.DL_GPIO_getEnabledInterruptStatus  bsp_motor_hallencoder.o
    0x0000296c   0x0000296c   0x00000014   Code   RO          301    .text.DL_GPIO_initDigitalInput  iic.o
    0x00002980   0x00002980   0x00000030   Code   RO          125    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x000029b0   0x000029b0   0x00000014   Code   RO          127    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x000029c4   0x000029c4   0x00000014   Code   RO          287    .text.DL_GPIO_initDigitalOutput  iic.o
    0x000029d8   0x000029d8   0x0000002c   Code   RO          123    .text.DL_GPIO_initDigitalOutputFeatures  ti_msp_dl_config.o
    0x00002a04   0x00002a04   0x00000014   Code   RO          111    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00002a18   0x00002a18   0x00000018   Code   RO          121    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00002a30   0x00002a30   0x00000038   Code   RO          117    .text.DL_GPIO_initPeripheralInputFunctionFeatures  ti_msp_dl_config.o
    0x00002a68   0x00002a68   0x0000001c   Code   RO          113    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00002a84   0x00002a84   0x00000016   Code   RO          303    .text.DL_GPIO_readPins  iic.o
    0x00002a9a   0x00002a9a   0x00000016   Code   RO          463    .text.DL_GPIO_readPins  bsp_motor_hallencoder.o
    0x00002ab0   0x00002ab0   0x00000010   Code   RO           91    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00002ac0   0x00002ac0   0x0000001c   Code   RO          133    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00002adc   0x00002adc   0x00000014   Code   RO          129    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00002af0   0x00002af0   0x00000014   Code   RO          293    .text.DL_GPIO_setPins  iic.o
    0x00002b04   0x00002b04   0x00000018   Code   RO          334    .text.DL_GPIO_setPins  hw_lcd.o
    0x00002b1c   0x00002b1c   0x00000018   Code   RO          135    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x00002b34   0x00002b34   0x00000018   Code   RO          167    .text.DL_I2C_enableAnalogGlitchFilter  ti_msp_dl_config.o
    0x00002b4c   0x00002b4c   0x00000014   Code   RO          179    .text.DL_I2C_enableController  ti_msp_dl_config.o
    0x00002b60   0x00002b60   0x00000018   Code   RO          177    .text.DL_I2C_enableControllerClockStretching  ti_msp_dl_config.o
    0x00002b78   0x00002b78   0x00000014   Code   RO          105    .text.DL_I2C_enablePower  ti_msp_dl_config.o
    0x00002b8c   0x00002b8c   0x00000010   Code   RO           95    .text.DL_I2C_reset  ti_msp_dl_config.o
    0x00002b9c   0x00002b9c   0x00000014   Code   RO          169    .text.DL_I2C_resetControllerTransfer  ti_msp_dl_config.o
    0x00002bb0   0x00002bb0   0x00000026   Code   RO          165    .text.DL_I2C_setAnalogGlitchFilterPulseWidth  ti_msp_dl_config.o
    0x00002bd6   0x00002bd6   0x00000026   Code   RO          603    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00002bfc   0x00002bfc   0x00000024   Code   RO          175    .text.DL_I2C_setControllerRXFIFOThreshold  ti_msp_dl_config.o
    0x00002c20   0x00002c20   0x00000028   Code   RO          173    .text.DL_I2C_setControllerTXFIFOThreshold  ti_msp_dl_config.o
    0x00002c48   0x00002c48   0x00000018   Code   RO          171    .text.DL_I2C_setTimerPeriod  ti_msp_dl_config.o
    0x00002c60   0x00002c60   0x00000018   Code   RO          193    .text.DL_SPI_enable  ti_msp_dl_config.o
    0x00002c78   0x00002c78   0x00000018   Code   RO          109    .text.DL_SPI_enablePower  ti_msp_dl_config.o
    0x00002c90   0x00002c90   0x00000044   Code   RO          635    .text.DL_SPI_init   driverlib.a(dl_spi.o)
    0x00002cd4   0x00002cd4   0x00000018   Code   RO          328    .text.DL_SPI_isBusy  hw_lcd.o
    0x00002cec   0x00002cec   0x00000018   Code   RO           99    .text.DL_SPI_reset  ti_msp_dl_config.o
    0x00002d04   0x00002d04   0x00000020   Code   RO          189    .text.DL_SPI_setBitRateSerialClockDivider  ti_msp_dl_config.o
    0x00002d24   0x00002d24   0x00000012   Code   RO          637    .text.DL_SPI_setClockConfig  driverlib.a(dl_spi.o)
    0x00002d36   0x00002d36   0x00000002   PAD
    0x00002d38   0x00002d38   0x00000030   Code   RO          191    .text.DL_SPI_setFIFOThreshold  ti_msp_dl_config.o
    0x00002d68   0x00002d68   0x00000016   Code   RO          326    .text.DL_SPI_transmitData8  hw_lcd.o
    0x00002d7e   0x00002d7e   0x00000002   PAD
    0x00002d80   0x00002d80   0x000000c0   Code   RO          830    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00002e40   0x00002e40   0x0000000c   Code   RO          147    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00002e4c   0x00002e4c   0x00000014   Code   RO          149    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00002e60   0x00002e60   0x00000010   Code   RO          153    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x00002e70   0x00002e70   0x00000018   Code   RO          141    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00002e88   0x00002e88   0x0000001c   Code   RO          143    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x00002ea4   0x00002ea4   0x00000050   Code   RO          844    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00002ef4   0x00002ef4   0x0000001c   Code   RO          145    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00002f10   0x00002f10   0x00000018   Code   RO          151    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00002f28   0x00002f28   0x00000028   Code   RO          838    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00002f50   0x00002f50   0x0000000c   Code   RO          197    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x00002f5c   0x00002f5c   0x00000028   Code   RO          195    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x00002f84   0x00002f84   0x00000014   Code   RO          159    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00002f98   0x00002f98   0x00000018   Code   RO          163    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00002fb0   0x00002fb0   0x00000018   Code   RO          103    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00002fc8   0x00002fc8   0x00000012   Code   RO           20    .text.DL_Timer_getPendingInterrupt  empty.o
    0x00002fda   0x00002fda   0x00000012   Code   RO          499    .text.DL_Timer_getPendingInterrupt  hw_timer.o
    0x00002fec   0x00002fec   0x00000108   Code   RO          769    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x000030f4   0x000030f4   0x000000ec   Code   RO          693    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x000031e0   0x000031e0   0x00000010   Code   RO           93    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x000031f0   0x000031f0   0x00000014   Code   RO          161    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00003204   0x00003204   0x0000001c   Code   RO          735    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00003220   0x00003220   0x00000018   Code   RO          743    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00003238   0x00003238   0x00000010   Code   RO          695    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00003248   0x00003248   0x0000001c   Code   RO          689    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00003264   0x00003264   0x0000003c   Code   RO          157    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x000032a0   0x000032a0   0x00000016   Code   RO          187    .text.DL_UART_enable  ti_msp_dl_config.o
    0x000032b6   0x000032b6   0x00000002   PAD
    0x000032b8   0x000032b8   0x0000001c   Code   RO          185    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x000032d4   0x000032d4   0x00000018   Code   RO          107    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x000032ec   0x000032ec   0x00000012   Code   RO           14    .text.DL_UART_getPendingInterrupt  empty.o
    0x000032fe   0x000032fe   0x00000002   PAD
    0x00003300   0x00003300   0x00000048   Code   RO          790    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00003348   0x00003348   0x00000018   Code   RO          258    .text.DL_UART_isBusy  usart.o
    0x00003360   0x00003360   0x00000014   Code   RO           16    .text.DL_UART_receiveData  empty.o
    0x00003374   0x00003374   0x00000018   Code   RO           97    .text.DL_UART_reset  ti_msp_dl_config.o
    0x0000338c   0x0000338c   0x0000004c   Code   RO          183    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000033d8   0x000033d8   0x00000012   Code   RO          792    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000033ea   0x000033ea   0x0000001e   Code   RO          181    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00003408   0x00003408   0x00000016   Code   RO          260    .text.DL_UART_transmitData  usart.o
    0x0000341e   0x0000341e   0x00000002   PAD
    0x00003420   0x00003420   0x00000130   Code   RO          459    .text.GROUP1_IRQHandler  bsp_motor_hallencoder.o
    0x00003550   0x00003550   0x000000ac   Code   RO          299    .text.I2C_WaitAck   iic.o
    0x000035fc   0x000035fc   0x00000078   Code   RO          297    .text.IIC_Send_Ack  iic.o
    0x00003674   0x00003674   0x00000058   Code   RO          285    .text.IIC_Start     iic.o
    0x000036cc   0x000036cc   0x0000004c   Code   RO          295    .text.IIC_Stop      iic.o
    0x00003718   0x00003718   0x00000014   Code   RO          336    .text.LCD_WR_DATA8  hw_lcd.o
    0x0000372c   0x0000372c   0x0000002c   Code   RO          340    .text.LCD_WR_REG    hw_lcd.o
    0x00003758   0x00003758   0x00000030   Code   RO          330    .text.LCD_Writ_Bus  hw_lcd.o
    0x00003788   0x00003788   0x00000098   Code   RO          307    .text.Read_Byte     iic.o
    0x00003820   0x00003820   0x000001e4   Code   RO           67    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00003a04   0x00003a04   0x00000054   Code   RO           79    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00003a58   0x00003a58   0x0000008c   Code   RO           73    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00003ae4   0x00003ae4   0x00000068   Code   RO           71    .text.SYSCFG_DL_PWM_LED_init  ti_msp_dl_config.o
    0x00003b4c   0x00003b4c   0x0000003c   Code   RO           83    .text.SYSCFG_DL_SPI_LCD_init  ti_msp_dl_config.o
    0x00003b88   0x00003b88   0x00000058   Code   RO           69    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00003be0   0x00003be0   0x0000000e   Code   RO           85    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00003bee   0x00003bee   0x00000002   PAD
    0x00003bf0   0x00003bf0   0x00000030   Code   RO           75    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00003c20   0x00003c20   0x00000038   Code   RO           77    .text.SYSCFG_DL_TIMER_TICK_init  ti_msp_dl_config.o
    0x00003c58   0x00003c58   0x00000044   Code   RO           81    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00003c9c   0x00003c9c   0x00000058   Code   RO           63    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00003cf4   0x00003cf4   0x000000a0   Code   RO           65    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003d94   0x00003d94   0x00000094   Code   RO          305    .text.Send_Byte     iic.o
    0x00003e28   0x00003e28   0x00000020   Code   RO          497    .text.TIMA0_IRQHandler  hw_timer.o
    0x00003e48   0x00003e48   0x000000b4   Code   RO           18    .text.TIMG0_IRQHandler  empty.o
    0x00003efc   0x00003efc   0x00000030   Code   RO           12    .text.UART0_IRQHandler  empty.o
    0x00003f2c   0x00003f2c   0x00000028   Code   RO            6    .text.__NVIC_ClearPendingIRQ  empty.o
    0x00003f54   0x00003f54   0x00000028   Code   RO          443    .text.__NVIC_ClearPendingIRQ  bsp_motor_hallencoder.o
    0x00003f7c   0x00003f7c   0x0000002c   Code   RO          493    .text.__NVIC_ClearPendingIRQ  hw_timer.o
    0x00003fa8   0x00003fa8   0x00000028   Code   RO            8    .text.__NVIC_EnableIRQ  empty.o
    0x00003fd0   0x00003fd0   0x00000028   Code   RO          445    .text.__NVIC_EnableIRQ  bsp_motor_hallencoder.o
    0x00003ff8   0x00003ff8   0x0000002c   Code   RO          495    .text.__NVIC_EnableIRQ  hw_timer.o
    0x00004024   0x00004024   0x00000084   Code   RO          155    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x000040a8   0x000040a8   0x00000008   Code   RO            2    .text._sys_exit     empty.o
    0x000040b0   0x000040b0   0x00000016   Code   RO          573    .text.delay_ms      time.o
    0x000040c6   0x000040c6   0x00000002   PAD
    0x000040c8   0x000040c8   0x00000074   Code   RO          571    .text.delay_us      time.o
    0x0000413c   0x0000413c   0x00000016   Code   RO          441    .text.encoder_init  bsp_motor_hallencoder.o
    0x00004152   0x00004152   0x00000002   PAD
    0x00004154   0x00004154   0x0000001c   Code   RO          455    .text.encoder_update_L  bsp_motor_hallencoder.o
    0x00004170   0x00004170   0x0000001c   Code   RO          457    .text.encoder_update_R  bsp_motor_hallencoder.o
    0x0000418c   0x0000418c   0x0000002c   Code   RO          264    .text.fputc         usart.o
    0x000041b8   0x000041b8   0x000001d4   Code   RO          311    .text.get_angle     iic.o
    0x0000438c   0x0000438c   0x000000a4   Code   RO          281    .text.jy61pInit     iic.o
    0x00004430   0x00004430   0x000001d0   Code   RO          344    .text.lcd_init      hw_lcd.o
    0x00004600   0x00004600   0x000000c4   Code   RO            4    .text.main          empty.o
    0x000046c4   0x000046c4   0x000000d4   Code   RO          309    .text.readDataJy61p  iic.o
    0x00004798   0x00004798   0x0000002c   Code   RO          324    .text.spi_write_bus  hw_lcd.o
    0x000047c4   0x000047c4   0x00000016   Code   RO          491    .text.timer_init    hw_timer.o
    0x000047da   0x000047da   0x00000002   PAD
    0x000047dc   0x000047dc   0x0000002c   Code   RO          256    .text.uart0_send_char  usart.o
    0x00004808   0x00004808   0x0000003c   Code   RO          262    .text.uart0_send_string  usart.o
    0x00004844   0x00004844   0x00000098   Code   RO          283    .text.writeDataJy61p  iic.o
    0x000048dc   0x000048dc   0x00000030   Code   RO         1140    i.__ARM_common_ll_muluu  c_p.l(btod.o)
    0x0000490c   0x0000490c   0x0000002c   Code   RO         1167    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00004938   0x00004938   0x00000016   Code   RO          890    i._dgeq             fz_ps.l(dcmp.o)
    0x0000494e   0x0000494e   0x0000001a   Code   RO          892    i._dleq             fz_ps.l(dcmp.o)
    0x00004968   0x00004968   0x0000000e   Code   RO         1012    i._is_digit         c_p.l(__printf_wp.o)
    0x00004976   0x00004976   0x00000002   PAD
    0x00004978   0x00004978   0x0000002c   Code   RO         1145    locale$$code        c_p.l(lc_numeric_c.o)
    0x000049a4   0x000049a4   0x0000002c   Code   RO         1177    locale$$code        c_p.l(lc_ctype_c.o)
    0x000049d0   0x000049d0   0x00000064   Code   RO         1047    x$fpl$dgeqf         fz_ps.l(dgef.o)
    0x00004a34   0x00004a34   0x00000064   Code   RO         1049    x$fpl$dleqf         fz_ps.l(dlef.o)
    0x00004a98   0x00004a98   0x00000010   Code   RO         1059    x$fpl$printf1       fz_ps.l(printf1.o)
    0x00004aa8   0x00004aa8   0x00000010   Code   RO         1061    x$fpl$printf2       fz_ps.l(printf2.o)
    0x00004ab8   0x00004ab8   0x00000000   Code   RO         1067    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00004ab8   0x00004ab8   0x00000080   Data   RO          902    .constdata          fz_ps.l(ddiv.o)
    0x00004b38   0x00004b38   0x00000008   Data   RO          971    .constdata          c_p.l(_printf_wctomb.o)
    0x00004b40   0x00004b40   0x00000028   Data   RO         1000    .constdata          c_p.l(_printf_hex_int_ll_ptr.o)
    0x00004b68   0x00004b68   0x00000011   Data   RO         1020    .constdata          c_p.l(__printf_flags_ss_wp.o)
    0x00004b79   0x00004b79   0x00000026   Data   RO         1099    .constdata          c_p.l(_printf_fp_hex.o)
    0x00004b9f   0x00004b9f   0x00000001   PAD
    0x00004ba0   0x00004ba0   0x00000094   Data   RO         1136    .constdata          c_p.l(bigflt0.o)
    0x00004c34   0x00004c34   0x00000002   Data   RO          214    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x00004c36   0x00004c36   0x00000003   Data   RO          208    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x00004c39   0x00004c39   0x00000003   PAD
    0x00004c3c   0x00004c3c   0x00000008   Data   RO          209    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00004c44   0x00004c44   0x00000003   Data   RO          206    .rodata.gPWM_LEDClockConfig  ti_msp_dl_config.o
    0x00004c47   0x00004c47   0x00000001   PAD
    0x00004c48   0x00004c48   0x00000008   Data   RO          207    .rodata.gPWM_LEDConfig  ti_msp_dl_config.o
    0x00004c50   0x00004c50   0x00000002   Data   RO          217    .rodata.gSPI_LCD_clockConfig  ti_msp_dl_config.o
    0x00004c52   0x00004c52   0x0000000a   Data   RO          218    .rodata.gSPI_LCD_config  ti_msp_dl_config.o
    0x00004c5c   0x00004c5c   0x00000028   Data   RO          205    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00004c84   0x00004c84   0x00000003   Data   RO          210    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00004c87   0x00004c87   0x00000001   PAD
    0x00004c88   0x00004c88   0x00000014   Data   RO          211    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00004c9c   0x00004c9c   0x00000003   Data   RO          212    .rodata.gTIMER_TICKClockConfig  ti_msp_dl_config.o
    0x00004c9f   0x00004c9f   0x00000001   PAD
    0x00004ca0   0x00004ca0   0x00000014   Data   RO          213    .rodata.gTIMER_TICKTimerConfig  ti_msp_dl_config.o
    0x00004cb4   0x00004cb4   0x00000002   Data   RO          215    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00004cb6   0x00004cb6   0x0000000a   Data   RO          216    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00004cc0   0x00004cc0   0x00000099   Data   RO           30    .rodata.str1.1      empty.o
    0x00004d59   0x00004d59   0x0000000e   Data   RO          314    .rodata.str1.1      iic.o
    0x00004d67   0x00004d67   0x00000001   PAD
    0x00004d68   0x00004d68   0x00000020   Data   RO         1310    Region$$Table       anon$$obj.o
    0x00004d88   0x00004d88   0x0000001c   Data   RO         1144    locale$$data        c_p.l(lc_numeric_c.o)
    0x00004da4   0x00004da4   0x00000110   Data   RO         1176    locale$$data        c_p.l(lc_ctype_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00004eb8, Size: 0x00000c20, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00004eb8   0x00000004   Data   RW           40    .data.TIMG0_IRQHandler.timer_count  empty.o
    0x20200004   0x00004ebc   0x00000004   PAD
    0x20200008        -       0x00000060   Zero   RW         1121    .bss                c_p.l(libspace.o)
    0x20200068        -       0x0000000c   Zero   RW          316    .bss.Gyro_Structure  iic.o
    0x20200074        -       0x00000001   Zero   RW           24    .bss.PID_flash_time  empty.o
    0x20200075   0x00004ebc   0x00000003   PAD
    0x20200078        -       0x00000054   Zero   RW           41    .bss.__stdout       empty.o
    0x202000cc        -       0x00000001   Zero   RW           23    .bss.bmq_flash_time  empty.o
    0x202000cd   0x00004ebc   0x00000003   PAD
    0x202000d0        -       0x000000a0   Zero   RW          202    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x20200170        -       0x000000a0   Zero   RW          201    .bss.gPWM_LEDBackup  ti_msp_dl_config.o
    0x20200210        -       0x00000028   Zero   RW          204    .bss.gSPI_LCDBackup  ti_msp_dl_config.o
    0x20200238        -       0x000000bc   Zero   RW          203    .bss.gTIMER_TICKBackup  ti_msp_dl_config.o
    0x202002f4        -       0x00000001   Zero   RW           22    .bss.led_flash_time  empty.o
    0x202002f5   0x00004ebc   0x00000003   PAD
    0x202002f8        -       0x00000010   Zero   RW          467    .bss.motor_encoder_L  bsp_motor_hallencoder.o
    0x20200308        -       0x00000010   Zero   RW          468    .bss.motor_encoder_R  bsp_motor_hallencoder.o
    0x20200318        -       0x00000100   Zero   RW           28    .bss.rx_buff        empty.o
    0x20200418        -       0x00000001   Zero   RW           26    .bss.uart_data      empty.o
    0x20200419   0x00004ebc   0x00000007   PAD
    0x20200420        -       0x00000400   Zero   RW           54    HEAP                startup_mspm0g350x_uvision.o
    0x20200820        -       0x00000400   Zero   RW           53    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       536         28          0          0         32       6700   bsp_motor_hallencoder.o
       568         80        153          4        344      10339   empty.o
       710         32          0          0          0      15921   hw_lcd.o
       160         12          0          0          0       5500   hw_timer.o
      1878         44         14          0         12       8302   iic.o
        48         22        192          0       2048        676   startup_mspm0g350x_uvision.o
      3016        260        134          0        548      37381   ti_msp_dl_config.o
       138          8          0          0          0       1114   time.o
       194         12          0          0          0       3373   usart.o

    ----------------------------------------------------------------------
      7264        <USER>        <GROUP>          4       3004      89306   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          7          0         20          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          6         17          0          0         76   __printf_flags_ss_wp.o
        14          0          0          0          0         60   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        66          0          0          0          0         76   _c16rtomb.o
        10          0          0          0          0          0   _printf_a.o
        10          0          0          0          0          0   _printf_c.o
        46          0          0          0          0        100   _printf_char.o
        48          6          0          0          0         88   _printf_char_common.o
        40          6          0          0          0         72   _printf_char_file.o
        38          0          0          0          0         60   _printf_charcount.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
        10          0          0          0          0          0   _printf_e.o
        10          0          0          0          0          0   _printf_f.o
      1044         12          0          0          0        116   _printf_fp_dec.o
       728         10         38          0          0         84   _printf_fp_hex.o
       136         16          0          0          0         76   _printf_fp_infnan.o
        10          0          0          0          0          0   _printf_g.o
       152          4         40          0          0        148   _printf_hex_int_ll_ptr.o
        10          0          0          0          0          0   _printf_i.o
       176          0          0          0          0         84   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
        10          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
        10          0          0          0          0          0   _printf_lld.o
        10          0          0          0          0          0   _printf_lli.o
        10          0          0          0          0          0   _printf_llo.o
        10          0          0          0          0          0   _printf_llu.o
        10          0          0          0          0          0   _printf_llx.o
       112         18          0          0          0         76   _printf_longlong_dec.o
        10          0          0          0          0          0   _printf_ls.o
        10          0          0          0          0          0   _printf_n.o
        10          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        112   _printf_oct_int_ll.o
        10          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        100   _printf_pad.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         72   _printf_str.o
        36          0          0          0          0         76   _printf_truncate.o
        10          0          0          0          0          0   _printf_u.o
        46          0          0          0          0        100   _printf_wchar.o
       188          6          8          0          0         80   _printf_wctomb.o
        10          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         60   _sputc.o
       504          4          0          0          0         92   aeabi_sdivfast.o
       216          6        148          0          0         80   bigflt0.o
      1456         30          0          0          0        336   btod.o
        16          0          0          0          0         68   exit.o
         8          0          0          0          0         60   ferror.o
         6          0          0          0          0        136   heapauxi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       122          0          0          0          0         72   lludiv10.o
        28          6          0          0          0         84   printf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        44          4          0          0          0         84   sprintf.o
       176          4          0          0          0         80   strcmpv6m.o
        62          0          0          0          0         80   sys_stackheap_outer.o
        10          0          0          0          0        803   dl_common.o
        38          0          0          0          0       8620   dl_i2c.o
        86          8          0          0          0      13518   dl_spi.o
       312         24          0          0          0      12877   dl_sysctl_mspm0g1x0x_g3x0x.o
       596        188          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
        46          0          0          0          0         60   cmpret.o
       124          4          0          0          0         72   d2f.o
       856         20          0          0          0        208   daddsub.o
        48          0          0          0          0        136   dcmp.o
       160          6          0          0          0         76   dcmpin.o
      1096         26        128          0          0        112   ddiv.o
        88          0          0          0          0         92   dflti.o
       100          4          0          0          0         92   dgef.o
       100          4          0          0          0         92   dlef.o
       584         26          0          0          0         84   dmul.o
        20          6          0          0          0         68   dnan2.o
        84          4          0          0          0         60   f2d.o
        76          0          0          0          0         68   ffixi.o
        16          4          0          0          0         76   printf1.o
        16          4          0          0          0         76   printf2.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
        44          4          0          0          0         60   fpclassify.o

    ----------------------------------------------------------------------
     11672        <USER>        <GROUP>          0         96      96602   Library Totals
        34          4          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6954        208        551          0         96       3564   c_p.l
      1132        228          0          0          0      91538   driverlib.a
      3508        108        128          0          0       1440   fz_ps.l
        44          4          0          0          0         60   m_ps.l

    ----------------------------------------------------------------------
     11672        <USER>        <GROUP>          0         96      96602   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     18936       1050       1212          4       3100     183264   Grand Totals
     18936       1050       1212          4       3100     183264   ELF Image Totals
     18936       1050       1212          4          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                20148 (  19.68kB)
    Total RW  Size (RW Data + ZI Data)              3104 (   3.03kB)
    Total ROM Size (Code + RO Data + RW Data)      20152 (  19.68kB)

==============================================================================

