 /************************* 头文件 *************************/
#include "ti_msp_dl_config.h"
#include "stdio.h"
#include "hw_timer.h"
#include "bsp_tb6612.h"
#include "hw_key.h"
#include "hw_lcd.h"
#include "string.h"
#include "LED.h"
#include "KEY.h"
#include "USART.h"
#include "PWM.h"
#include "IIC.h"
#include "OLED.h"
#include "bsp_motor_hallencoder.h"
#include "PID.h"
#include "Filter.h"
#include "Time.h"
#include "hardware_iic.h"
#include "Sensor.h"

#if !defined(__MICROLIB)
//不使用微库的话就需要添加下面的函数
#if (__ARMCLIB_VERSION <= 6000000)
//如果编译器是AC5  就定义下面这个结构体
struct __FILE
{
        int handle;
};
#endif
FILE __stdout;
//定义_sys_exit()以避免使用半主机模式
void _sys_exit(int x)
{
        x = x;
}
#endif

/************************* 宏定义 *************************/

//#define posion_pid_p 0.51f
//#define posion_pid_i 0.0f
//#define posion_pid_d 0.038f

#define posion_pid_p 10.0f
#define posion_pid_i 0.0f
#define posion_pid_d 0.5f

#define BASE_SPEED 40    //基础速度

#define left_pid_p 5.0f
#define left_pid_i 100.0f
#define left_pid_d 0.001f

#define right_pid_p 5.0f
#define right_pid_i 100.0f
#define right_pid_d 0.001f

Kalman_Filter left_kalman;
Kalman_Filter right_kalman;
Kalman_Filter posion_kalman;
/************************ 变量定义 ************************/
volatile uint8_t led_flash_time=0;
volatile uint8_t bmq_flash_time=0;
volatile uint8_t PID_flash_time=0;
uint16_t temporary_str[20];


volatile unsigned int delay_times = 0;
volatile unsigned char uart_data = 0;




IncrementalPID left_speed_pid;
IncrementalPID right_speed_pid;
IncrementalPID posion_pid;
int llll,rrrr;


unsigned char Digtal;
unsigned char Anolog[8]={0};
unsigned char rx_buff[256]={0};
unsigned char Normal[8]={0};
    
/************************ 函数定义 ************************/
void ui_home_page(void);// 首页页面初始化


void PID_operation(void);//PID操作
void PID_SendWave(int a,int b,int c,int d);
//#if !defined(__MICROLIB)
////不使用微库的话就需要添加下面的函数
//#if (__ARMCLIB_VERSION <= 6000000)
////如果编译器是AC5  就定义下面这个结构体
//struct __FILE
//{
//        int handle;
//};
//#endif

//FILE __stdout;

////定义_sys_exit()以避免使用半主机模式
//void _sys_exit(int x)
//{
//        x = x;
//}
//#endif

int main(void)
{
    SYSCFG_DL_init();
    //使能外部中断
//	NVIC_EnableIRQ (KEY1_INT_IRQN );
		timer_init();
	//清除定时器中断标志
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //使能定时器中断
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	
	//清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
//    jy61pInit();

	  sprintf((char *)rx_buff,"hello_world!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);	
		
    printf("\r\nJY61P 3D Attitude Measurement Sensor Start...\r\n");

    while (1)
    {
        Gyro_Struct *JY61P_Data = get_angle();
        printf("\n");
        printf("JY61P RollX  = [ %d ]\r\n",(int)JY61P_Data->x);
        printf("JY61P PitchY = [ %d ]\r\n",(int)JY61P_Data->y);
        printf("JY61P YawZ   = [ %d ]\r\n",(int)JY61P_Data->z);
        delay_ms(100);
    }

	
	///////////////////////////////以上用于调试陀螺仪////////////////////////////////
	
	PID_Init(&left_speed_pid,left_pid_p,left_pid_i,left_pid_d);
	PID_Init(&right_speed_pid,right_pid_p,right_pid_i,right_pid_d);
	PID_Init(&posion_pid,posion_pid_p,posion_pid_i,posion_pid_d);
    
	//卡尔曼滤波参数
	Kalman_Init(&posion_kalman,0.0f,1.0f,0.1f,0.01f);
	Kalman_Init(&left_kalman,0.0f,1.0f,0.1f,0.01f);
	Kalman_Init(&right_kalman,0.0f,1.0f,0.1f,0.01f);
	
	
    SYSCFG_DL_init();
    //使能外部中断
//	NVIC_EnableIRQ (KEY1_INT_IRQN );
	timer_init();
	//清除定时器中断标志
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //使能定时器中断
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	
	//清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
	
	
		encoder_init();
		SYSCFG_DL_init();
    lcd_init();
		timer_init();
	
	


	    sprintf((char *)rx_buff,"hello_world!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);	
		while(Ping())
		{
			delay_ms(1);
			sprintf((char *)rx_buff,"Ping Faild Try Again!\r\n");
			uart0_send_string((char *)rx_buff);
			memset(rx_buff,0,256);
		}
		sprintf((char *)rx_buff,"Ping Succseful!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);
	
	
	
	while(1)
	{
		   
			
//			//获取传感器模拟量结果  
//			if(IIC_Get_Anolog(Anolog,8)){
//			sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
//			uart0_send_string((char *)rx_buff);
//			memset(rx_buff,0,256);
//			}
//			
//			//获取传感器归一化结果
//			if(IIC_Get_Normalize(Normal,8)){
//			sprintf((char *)rx_buff,"Normalize %d-%d-%d-%d-%d-%d-%d-%d\r\n",Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
//			uart0_send_string((char *)rx_buff);
//			memset(rx_buff,0,256);
//			}
		
		
//		AB_Control(200,200);
//		int	left_speed_L=get_encoder_count_L();//获取左编码器速度
//	    int	left_speed_R=get_encoder_count_R();//获取右编码器速度
		//printf("w:%d,%d,%d,%d\r\n",left_speed,right_speed,BASE_SPEED,BASE_SPEED);
//		AO_Control(1,100);//设置速度
//	    BO_Control(1,100);	
//		AB_Control(50,50);
		if(PID_flash_time)
		{
			PID_flash_time=0;
		    PID_operation();
		}
		
	
	}

}
	
//PID操作
void PID_operation(void)
{
	static float total_left_speed=0;//总左移速度
	static float total_right_speed=0;//总右移速度
	static float total_speed=0;//位置环累积
//	 Digtal=IIC_Get_Digtal();
//	 sprintf((char *)rx_buff,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
//	 uart0_send_string((char *)rx_buff);
//	 memset(rx_buff,0,256);
	
	
	float posion=UpdatePosition();       //获取位置值
	int	left_speed=get_encoder_count_L();//获取左编码器速度
	int	right_speed=get_encoder_count_R();//获取右编码器速度
	
	float left_speed_filter	=Kalman_Update(&left_kalman,left_speed);//左速度滤波值 = 卡尔曼更新
	float right_speed_filter=Kalman_Update(&right_kalman,right_speed);//右速度滤波值 = 卡尔曼更新
	float posion_filter=Kalman_Update(&posion_kalman,posion);         //位置滤波值
	
	
	float   adjust=PID_Calculate(&posion_pid,0.02f,0,posion_filter);//调整 = PID计算
	total_speed+=adjust;
	float	left_speed_adjust=BASE_SPEED-total_speed; //左速度调整 = 基础速度-调整  得到新的期望值
	float   right_speed_adjust=BASE_SPEED+total_speed;//右速度调整 = 基础速度-调整    得到新的期望值
	
	float adjust_left_speed =PID_Calculate(&left_speed_pid,0.02f,left_speed_adjust,left_speed_filter);//左轮总速度增量
	float adjust_right_speed =PID_Calculate(&right_speed_pid,0.02f,right_speed_adjust,right_speed_filter);//右轮总速度增量
	
	
	total_left_speed=total_left_speed+adjust_left_speed;
	total_right_speed=total_right_speed+adjust_right_speed;
	
//    float left_speed_filter_adjust=Kalman_Update(&left_kalman,total_left_speed);//左速度滤波值 = 卡尔曼更新
//	float right_speed_filter_adjust =Kalman_Update(&right_kalman,total_right_speed);//右速度滤波值 = 卡尔曼更新
	
//	float	left_speed_adjust=BASE_SPEED-adjust; //左速度调整 = 基础速度-调整
//	float right_speed_adjust=BASE_SPEED+adjust;//右速度调整 = 基础速度-调整
	
	AB_Control(total_left_speed,total_right_speed);
	Num_L=total_left_speed;
	Num_R=total_right_speed;
	printf("w:%f,%f,%d,%d\r\n",left_speed_filter,right_speed_filter,BASE_SPEED,left_speed);
	//PID_SendWave(BASE_SPEED,Num_L,BASE_SPEED,Num_R);
//////////////////////////////////////////////////////////////////////////////////////////////
//		int total_left_speed=0;
//		int total_right_speed=0;
//	
//	    int	left_speed=get_encoder_count_L();//获取左编码器速度
//		int	right_speed=get_encoder_count_R();	//获取右编码器速度
//		float left_speed_filter	=Kalman_Update(&left_kalman,left_speed);//左速度滤波值 = 卡尔曼更新
//		float right_speed_filter=Kalman_Update(&right_kalman,right_speed);//右速度滤波值 = 卡尔曼更新
//		float posion_filter=Kalman_Update(&posion_kalman,posion);//位置滤波值
//		float adjust=PID_Calculate(&posion_pid,0.01f,0,posion_filter);//调整 = PID计算
//		float	left_speed_adjust=BASE_SPEED-adjust; //左速度调整 = 基础速度-调整
//		float right_speed_adjust=BASE_SPEED+adjust;//右速度调整 = 基础速度-调整
//    total_left_speed =PID_Calculate(&left_speed_pid,0.01f,left_speed_adjust,left_speed_filter);//左轮总速度
//    total_right_speed =PID_Calculate(&right_speed_pid,0.01f,right_speed_adjust,right_speed_filter);//右轮总速度
//		AO_Control(0,total_left_speed);//设置速度
//	    BO_Control(0,total_right_speed);	
}


//串口的中断服务函数
void UART_0_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        case DL_UART_IIDX_RX://如果是接收中断
            //接发送过来的数据保存在变量中
            uart_data = DL_UART_Main_receiveData(UART_0_INST);
            //将保存的数据再发送出去
            uart0_send_char(uart_data);
            break;
        default://其他的串口中断
            break;
    }
}

//定时器的中断服务函数 已配置为1ms的周期
void TIMER_0_INST_IRQHandler(void)
{
	
     static uint32_t timer_count=1;
	
    //如果产生了定时器中断
	if(DL_TimerG_getPendingInterrupt(TIMER_0_INST)==DL_TIMER_IIDX_ZERO)//如果是0溢出中断
	{
		
		timer_count=timer_count<1000?timer_count+1:1;
	    if(timer_count%1000==0) led_flash_time=1;
		if(timer_count%500==0) bmq_flash_time=1;
		if(timer_count%20==0)  PID_flash_time=1;
	}
	
}




/****************************End*****************************/


