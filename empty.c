 /************************* ͷ�ļ� *************************/
#include "ti_msp_dl_config.h"
#include "stdio.h"
#include "hw_timer.h"
#include "bsp_tb6612.h"
#include "hw_key.h"
#include "hw_lcd.h"
#include "string.h"
#include "LED.h"
#include "KEY.h"
#include "USART.h"
#include "PWM.h"
#include "IIC.h"
#include "OLED.h"
#include "bsp_motor_hallencoder.h"
#include "PID.h"
#include "Filter.h"
#include "Time.h"
#include "hardware_iic.h"
#include "Sensor.h"

#if !defined(__MICROLIB)
//��ʹ��΢��Ļ�����Ҫ��������ĺ���
#if (__ARMCLIB_VERSION <= 6000000)
//�����������AC5  �Ͷ�����������ṹ��
struct __FILE
{
        int handle;
};
#endif
FILE __stdout;
//����_sys_exit()�Ա���ʹ�ð�����ģʽ
void _sys_exit(int x)
{
        x = x;
}
#endif

/************************* �궨�� *************************/

//#define posion_pid_p 0.51f
//#define posion_pid_i 0.0f
//#define posion_pid_d 0.038f

#define posion_pid_p 10.0f
#define posion_pid_i 0.0f
#define posion_pid_d 0.5f

#define BASE_SPEED 40    //�����ٶ�

#define left_pid_p 5.0f
#define left_pid_i 100.0f
#define left_pid_d 0.001f

#define right_pid_p 5.0f
#define right_pid_i 100.0f
#define right_pid_d 0.001f

Kalman_Filter left_kalman;
Kalman_Filter right_kalman;
Kalman_Filter posion_kalman;
/************************ �������� ************************/
volatile uint8_t led_flash_time=0;
volatile uint8_t bmq_flash_time=0;
volatile uint8_t PID_flash_time=0;
uint16_t temporary_str[20];


volatile unsigned int delay_times = 0;
volatile unsigned char uart_data = 0;




IncrementalPID left_speed_pid;
IncrementalPID right_speed_pid;
IncrementalPID posion_pid;
int llll,rrrr;


unsigned char Digtal;
unsigned char Anolog[8]={0};
unsigned char rx_buff[256]={0};
unsigned char Normal[8]={0};
    
/************************ �������� ************************/
void ui_home_page(void);// ��ҳҳ���ʼ��


void PID_operation(void);//PID����
void PID_SendWave(int a,int b,int c,int d);
//#if !defined(__MICROLIB)
////��ʹ��΢��Ļ�����Ҫ��������ĺ���
//#if (__ARMCLIB_VERSION <= 6000000)
////�����������AC5  �Ͷ�����������ṹ��
//struct __FILE
//{
//        int handle;
//};
//#endif

//FILE __stdout;

////����_sys_exit()�Ա���ʹ�ð�����ģʽ
//void _sys_exit(int x)
//{
//        x = x;
//}
//#endif

int main(void)
{
    SYSCFG_DL_init();
    //ʹ���ⲿ�ж�
//	NVIC_EnableIRQ (KEY1_INT_IRQN );
	timer_init();
	//�����ʱ���жϱ�־
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //ʹ�ܶ�ʱ���ж�
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	
	//��������жϱ�־
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //ʹ�ܴ����ж�
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
	
		encoder_init();
		SYSCFG_DL_init();
    lcd_init();
		timer_init();

    jy61pInit();
	
	    sprintf((char *)rx_buff,"hello_world!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);	
		
    printf("\r\nJY61P 3D Attitude Measurement Sensor Start...\r\n");

    while (1)
    {
        Gyro_Struct *JY61P_Data = get_angle();

        printf("\n");
        printf("JY61P RollX  = [ %d ]\r\n",(int)JY61P_Data->x);
        printf("JY61P PitchY = [ %d ]\r\n",(int)JY61P_Data->y);
        printf("JY61P YawZ   = [ %d ]\r\n",(int)JY61P_Data->z);
        delay_ms(100);
    }

	
	
	///////////////////////////////�������ڵ���������////////////////////////////////
	
	PID_Init(&left_speed_pid,left_pid_p,left_pid_i,left_pid_d);
	PID_Init(&right_speed_pid,right_pid_p,right_pid_i,right_pid_d);
	PID_Init(&posion_pid,posion_pid_p,posion_pid_i,posion_pid_d);
    
	//�������˲�����
	Kalman_Init(&posion_kalman,0.0f,1.0f,0.1f,0.01f);
	Kalman_Init(&left_kalman,0.0f,1.0f,0.1f,0.01f);
	Kalman_Init(&right_kalman,0.0f,1.0f,0.1f,0.01f);
	
	
    SYSCFG_DL_init();
    //ʹ���ⲿ�ж�
//	NVIC_EnableIRQ (KEY1_INT_IRQN );
	timer_init();
	//�����ʱ���жϱ�־
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //ʹ�ܶ�ʱ���ж�
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	
	//��������жϱ�־
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //ʹ�ܴ����ж�
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
	
	
		encoder_init();
		SYSCFG_DL_init();
    lcd_init();
		timer_init();
	
	


	    sprintf((char *)rx_buff,"hello_world!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);	
		while(Ping())
		{
			delay_ms(1);
			sprintf((char *)rx_buff,"Ping Faild Try Again!\r\n");
			uart0_send_string((char *)rx_buff);
			memset(rx_buff,0,256);
		}
		sprintf((char *)rx_buff,"Ping Succseful!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff,0,256);
	
	
	
	while(1)
	{
		   
			
//			//��ȡ������ģ�������  
//			if(IIC_Get_Anolog(Anolog,8)){
//			sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
//			uart0_send_string((char *)rx_buff);
//			memset(rx_buff,0,256);
//			}
//			
//			//��ȡ��������һ�����
//			if(IIC_Get_Normalize(Normal,8)){
//			sprintf((char *)rx_buff,"Normalize %d-%d-%d-%d-%d-%d-%d-%d\r\n",Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
//			uart0_send_string((char *)rx_buff);
//			memset(rx_buff,0,256);
//			}
		
		
//		AB_Control(200,200);
//		int	left_speed_L=get_encoder_count_L();//��ȡ��������ٶ�
//	    int	left_speed_R=get_encoder_count_R();//��ȡ�ұ������ٶ�
		//printf("w:%d,%d,%d,%d\r\n",left_speed,right_speed,BASE_SPEED,BASE_SPEED);
//		AO_Control(1,100);//�����ٶ�
//	    BO_Control(1,100);	
//		AB_Control(50,50);
		if(PID_flash_time)
		{
			PID_flash_time=0;
		    PID_operation();
		}
		
	
	}

}
	
//PID����
void PID_operation(void)
{
	static float total_left_speed=0;//�������ٶ�
	static float total_right_speed=0;//�������ٶ�
	static float total_speed=0;//λ�û��ۻ�
//	 Digtal=IIC_Get_Digtal();
//	 sprintf((char *)rx_buff,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
//	 uart0_send_string((char *)rx_buff);
//	 memset(rx_buff,0,256);
	
	
	float posion=UpdatePosition();       //��ȡλ��ֵ
	int	left_speed=get_encoder_count_L();//��ȡ��������ٶ�
	int	right_speed=get_encoder_count_R();//��ȡ�ұ������ٶ�
	
	float left_speed_filter	=Kalman_Update(&left_kalman,left_speed);//���ٶ��˲�ֵ = ����������
	float right_speed_filter=Kalman_Update(&right_kalman,right_speed);//���ٶ��˲�ֵ = ����������
	float posion_filter=Kalman_Update(&posion_kalman,posion);         //λ���˲�ֵ
	
	
	float   adjust=PID_Calculate(&posion_pid,0.02f,0,posion_filter);//���� = PID����
	total_speed+=adjust;
	float	left_speed_adjust=BASE_SPEED-total_speed; //���ٶȵ��� = �����ٶ�-����  �õ��µ�����ֵ
	float   right_speed_adjust=BASE_SPEED+total_speed;//���ٶȵ��� = �����ٶ�-����    �õ��µ�����ֵ
	
	float adjust_left_speed =PID_Calculate(&left_speed_pid,0.02f,left_speed_adjust,left_speed_filter);//�������ٶ�����
	float adjust_right_speed =PID_Calculate(&right_speed_pid,0.02f,right_speed_adjust,right_speed_filter);//�������ٶ�����
	
	
	total_left_speed=total_left_speed+adjust_left_speed;
	total_right_speed=total_right_speed+adjust_right_speed;
	
//    float left_speed_filter_adjust=Kalman_Update(&left_kalman,total_left_speed);//���ٶ��˲�ֵ = ����������
//	float right_speed_filter_adjust =Kalman_Update(&right_kalman,total_right_speed);//���ٶ��˲�ֵ = ����������
	
//	float	left_speed_adjust=BASE_SPEED-adjust; //���ٶȵ��� = �����ٶ�-����
//	float right_speed_adjust=BASE_SPEED+adjust;//���ٶȵ��� = �����ٶ�-����
	
	AB_Control(total_left_speed,total_right_speed);
	Num_L=total_left_speed;
	Num_R=total_right_speed;
	printf("w:%f,%f,%d,%d\r\n",left_speed_filter,right_speed_filter,BASE_SPEED,left_speed);
	//PID_SendWave(BASE_SPEED,Num_L,BASE_SPEED,Num_R);
//////////////////////////////////////////////////////////////////////////////////////////////
//		int total_left_speed=0;
//		int total_right_speed=0;
//	
//	    int	left_speed=get_encoder_count_L();//��ȡ��������ٶ�
//		int	right_speed=get_encoder_count_R();	//��ȡ�ұ������ٶ�
//		float left_speed_filter	=Kalman_Update(&left_kalman,left_speed);//���ٶ��˲�ֵ = ����������
//		float right_speed_filter=Kalman_Update(&right_kalman,right_speed);//���ٶ��˲�ֵ = ����������
//		float posion_filter=Kalman_Update(&posion_kalman,posion);//λ���˲�ֵ
//		float adjust=PID_Calculate(&posion_pid,0.01f,0,posion_filter);//���� = PID����
//		float	left_speed_adjust=BASE_SPEED-adjust; //���ٶȵ��� = �����ٶ�-����
//		float right_speed_adjust=BASE_SPEED+adjust;//���ٶȵ��� = �����ٶ�-����
//    total_left_speed =PID_Calculate(&left_speed_pid,0.01f,left_speed_adjust,left_speed_filter);//�������ٶ�
//    total_right_speed =PID_Calculate(&right_speed_pid,0.01f,right_speed_adjust,right_speed_filter);//�������ٶ�
//		AO_Control(0,total_left_speed);//�����ٶ�
//	    BO_Control(0,total_right_speed);	
}


//���ڵ��жϷ�����
void UART_0_INST_IRQHandler(void)
{
    //��������˴����ж�
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        case DL_UART_IIDX_RX://����ǽ����ж�
            //�ӷ��͹��������ݱ����ڱ�����
            uart_data = DL_UART_Main_receiveData(UART_0_INST);
            //������������ٷ��ͳ�ȥ
            uart0_send_char(uart_data);
            break;

        default://�����Ĵ����ж�
            break;
    }
}

//��ʱ�����жϷ����� ������Ϊ1ms������
void TIMER_0_INST_IRQHandler(void)
{
	
     static uint32_t timer_count=1;
	
    //��������˶�ʱ���ж�
	if(DL_TimerG_getPendingInterrupt(TIMER_0_INST)==DL_TIMER_IIDX_ZERO)//�����0����ж�
	{
		
		timer_count=timer_count<1000?timer_count+1:1;
	    if(timer_count%1000==0) led_flash_time=1;
		if(timer_count%500==0) bmq_flash_time=1;
		if(timer_count%20==0)  PID_flash_time=1;
	}
	
}




/****************************End*****************************/


